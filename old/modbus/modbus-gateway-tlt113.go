package main

import (
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"net"
	"os"
	"os/signal"
	"runtime"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/tarm/serial"
	"gopkg.in/yaml.v2"
)

// Config 定义配置文件结构
type Config struct {
	Serial struct {
		MasterDevice string `yaml:"master_device"`
		SlaveDevice  string `yaml:"slave_device"`
		BaudRate     uint32 `yaml:"baud_rate"`
		DataBits     int    `yaml:"data_bits"`
		Parity       string `yaml:"parity"`
		StopBits     int    `yaml:"stop_bits"`
		TimeoutMs    int    `yaml:"timeout_ms"`
		Mode         string `yaml:"mode"`
	} `yaml:"serial"`
	Network struct {
		TCPPort    int `yaml:"tcp_port"`
		ConfigPort int `yaml:"config_port"`
		MaxClients int `yaml:"max_clients"`
	} `yaml:"network"`
	Modbus struct {
		MaxADULength int `yaml:"max_adu_length"`
	} `yaml:"modbus"`
	Runtime struct {
		QueueSize int `yaml:"queue_size"`
	} `yaml:"runtime"`
}

// SerialConfigManager 用于管理串口配置
type SerialConfigManager struct {
	currentConfig Config
	configLock    sync.RWMutex
	reloadChan    chan struct{}
}

var (
	config                Config
	configManager         *SerialConfigManager
	running               = true
	masterFd              *serial.Port
	slaveFd               *serial.Port
	serverSock            net.Listener
	configSock            net.Listener
	queue                 chan Request
	wg                    sync.WaitGroup
	pool                  sync.Pool
	slaveMutex            sync.Mutex
	configReloadInProgress int32
	configFilePath         string // 保存加载的配置文件路径
)

type RequestType int

const (
	TCP_REQ RequestType = iota
	RTU_REQ
	ASCII_REQ
)

// Request 定义请求结构
type Request struct {
	Type       RequestType
	ClientSock net.Conn
	Data       []byte
}

// loadConfig 加载配置文件
func loadConfig() error {
	// 默认配置
	config = Config{
		Serial: struct {
			MasterDevice string `yaml:"master_device"`
			SlaveDevice  string `yaml:"slave_device"`
			BaudRate     uint32 `yaml:"baud_rate"`
			DataBits     int    `yaml:"data_bits"`
			Parity       string `yaml:"parity"`
			StopBits     int    `yaml:"stop_bits"`
			TimeoutMs    int    `yaml:"timeout_ms"`
			Mode         string `yaml:"mode"`
		}{
			MasterDevice: "/dev/ttyS3",
			SlaveDevice:  "/dev/ttyS4",
			BaudRate:     115200,
			DataBits:     8,
			Parity:       "N",
			StopBits:     1,
			TimeoutMs:    1000,
			Mode:         "RTU",
		},
		Network: struct {
			TCPPort    int `yaml:"tcp_port"`
			ConfigPort int `yaml:"config_port"`
			MaxClients int `yaml:"max_clients"`
		}{
			TCPPort:    1502,
			ConfigPort: 1503,
			MaxClients: 5,
		},
		Modbus: struct {
			MaxADULength int `yaml:"max_adu_length"`
		}{
			MaxADULength: 256,
		},
		Runtime: struct {
			QueueSize int `yaml:"queue_size"`
		}{
			QueueSize: 500,
		},
	}

	// 检查命令行参数
	var configFile string
	if len(os.Args) > 1 {
		configFile = os.Args[1] // 第一个参数作为配置文件路径
	} else {
		// 未指定参数时，尝试默认路径
		defaultPaths := []string{"./config.yaml", "/etc/modbus-gateway/config.yaml"}
		for _, path := range defaultPaths {
			if _, err := os.Stat(path); err == nil {
				configFile = path
				break
			}
		}
	}

	// 如果找到配置文件，尝试加载
	if configFile != "" {
		data, err := os.ReadFile(configFile)
		if err != nil {
			log.Printf("Warning: failed to read config file %s, using default config: %v", configFile, err)
		} else {
			err = yaml.Unmarshal(data, &config)
			if err != nil {
				return fmt.Errorf("failed to parse config file %s: %v", configFile, err)
			}
			log.Printf("Loaded config from %s", configFile)
			configFilePath = configFile // 保存加载的路径
		}
	} else {
		log.Println("No config file specified or found, using default config")
		configFilePath = "./config.yaml" // 默认保存路径
	}

	// 环境变量覆盖
	if env := os.Getenv("MASTER_DEV"); env != "" {
		config.Serial.MasterDevice = env
	}
	if env := os.Getenv("SLAVE_DEV"); env != "" {
		config.Serial.SlaveDevice = env
	}
	if env := os.Getenv("MODE"); env != "" {
		config.Serial.Mode = env
	}

	return nil
}

// calculateCRC 计算CRC校验
func calculateCRC(buffer []byte) uint16 {
	crc := uint16(0xFFFF)
	for _, b := range buffer {
		crc ^= uint16(b)
		for i := 0; i < 8; i++ {
			if crc&0x0001 != 0 {
				crc = (crc >> 1) ^ 0xA001
			} else {
				crc >>= 1
			}
		}
	}
	return crc
}

// initSerial 初始化串口
func initSerial(device string) (*serial.Port, error) {
	configManager.configLock.RLock()
	defer configManager.configLock.RUnlock()

	var parity serial.Parity
	switch configManager.currentConfig.Serial.Parity {
	case "N":
		parity = serial.ParityNone
	case "E":
		parity = serial.ParityEven
	case "O":
		parity = serial.ParityOdd
	default:
		return nil, fmt.Errorf("invalid parity: %s", configManager.currentConfig.Serial.Parity)
	}

	var stopBits serial.StopBits
	switch configManager.currentConfig.Serial.StopBits {
	case 1:
		stopBits = serial.Stop1
	case 2:
		stopBits = serial.Stop2
	default:
		return nil, fmt.Errorf("invalid stop bits: %d", configManager.currentConfig.Serial.StopBits)
	}

	cfg := &serial.Config{
		Name:        device,
		Baud:        int(configManager.currentConfig.Serial.BaudRate),
		Size:        byte(configManager.currentConfig.Serial.DataBits),
		Parity:      parity,
		StopBits:    stopBits,
		ReadTimeout: time.Duration(configManager.currentConfig.Serial.TimeoutMs) * time.Millisecond,
	}
	return serial.OpenPort(cfg)
}

// flushSerial 清空串口缓冲区
func flushSerial(port *serial.Port) {
	tempBuffer := make([]byte, config.Modbus.MaxADULength)
	for {
		n, err := port.Read(tempBuffer)
		if n == 0 || err != nil {
			break
		}
	}
}

// readMasterRequest 读取Master请求帧
func readMasterRequest(r io.Reader, deadline time.Time) ([]byte, error) {
	if r == nil {
		return nil, fmt.Errorf("reader is nil")
	}

	data := make([]byte, 0, config.Modbus.MaxADULength)
	buf := make([]byte, 1)
	const (
		stateSlaveID = iota
		stateFunctionCode
		stateReadPayload
		stateCRC
	)
	state := stateSlaveID
	var toRead byte
	var crcCount int
	var functionCode byte

	for {
		if time.Now().After(deadline) {
			return nil, fmt.Errorf("timeout reading master request")
		}

		_, err := io.ReadAtLeast(r, buf, 1)
		if err != nil {
			return nil, fmt.Errorf("failed to read from master: %v", err)
		}

		switch state {
		case stateSlaveID:
			if buf[0] == 0 {
				continue
			}
			data = append(data, buf[0])
			state = stateFunctionCode

		case stateFunctionCode:
			functionCode = buf[0]
			data = append(data, functionCode)
			switch functionCode {
			case 1, 3:
				toRead = 4
				state = stateReadPayload
			case 5, 6:
				toRead = 4
				state = stateReadPayload
			case 16:
				toRead = 5
				state = stateReadPayload
			default:
				return nil, fmt.Errorf("unsupported function code: %d", functionCode)
			}

		case stateReadPayload:
			data = append(data, buf[0])
			toRead--
			if functionCode == 16 && toRead == 0 && len(data) == 7 {
				toRead = data[6]
				if toRead == 0 || int(toRead)+len(data)+2 > config.Modbus.MaxADULength {
					return nil, fmt.Errorf("invalid byte count for function 16: %d", toRead)
				}
			} else if toRead == 0 {
				state = stateCRC
			}

		case stateCRC:
			data = append(data, buf[0])
			crcCount++
			if crcCount == 2 {
				return data, nil
			}
		}
	}
}

// readSlaveResponse 读取Slave响应帧
func readSlaveResponse(r io.Reader, deadline time.Time, expectedSlaveID, expectedFunctionCode byte) ([]byte, error) {
	if r == nil {
		return nil, fmt.Errorf("reader is nil")
	}

	data := make([]byte, 0, config.Modbus.MaxADULength)
	buf := make([]byte, 1)
	const (
		stateSlaveID = iota
		stateFunctionCode
		stateReadPayload
		stateCRC
	)
	state := stateSlaveID
	var toRead byte
	var crcCount int
	var isException bool

	for {
		if time.Now().After(deadline) {
			return nil, fmt.Errorf("timeout reading slave response")
		}

		_, err := io.ReadAtLeast(r, buf, 1)
		if err != nil {
			return nil, fmt.Errorf("failed to read from slave: %v", err)
		}

		switch state {
		case stateSlaveID:
			if buf[0] != expectedSlaveID {
				continue
			}
			data = append(data, buf[0])
			state = stateFunctionCode

		case stateFunctionCode:
			data = append(data, buf[0])
			if buf[0] == expectedFunctionCode {
				switch expectedFunctionCode {
				case 1, 3:
					state = stateReadPayload
					toRead = 1
				case 5, 6:
					state = stateReadPayload
					toRead = 4
				case 16:
					state = stateReadPayload
					toRead = 4
				default:
					return nil, fmt.Errorf("unsupported function code: %d", expectedFunctionCode)
				}
			} else if buf[0] == expectedFunctionCode+0x80 {
				isException = true
				state = stateReadPayload
				toRead = 1
			} else {
				return nil, fmt.Errorf("unexpected function code: %d, expected: %d", buf[0], expectedFunctionCode)
			}

		case stateReadPayload:
			data = append(data, buf[0])
			toRead--
			if expectedFunctionCode == 1 || expectedFunctionCode == 3 {
				if toRead == 0 && len(data) == 3 && !isException {
					toRead = data[2]
					if toRead == 0 || int(toRead)+len(data)+2 > config.Modbus.MaxADULength {
						return nil, fmt.Errorf("invalid byte count: %d", toRead)
					}
				} else if toRead == 0 {
					state = stateCRC
				}
			} else if toRead == 0 {
				state = stateCRC
			}

		case stateCRC:
			data = append(data, buf[0])
			crcCount++
			if crcCount == 2 {
				return data, nil
			}
		}
	}
}

// worker 处理请求的工作线程（仅一个）
func worker(id int) {
	defer wg.Done()
	minElapsed := float64(1<<63 - 1)
	configManager.configLock.RLock()
	timeout := time.Duration(configManager.currentConfig.Serial.TimeoutMs) * time.Millisecond
	configManager.configLock.RUnlock()

	for running {
		if atomic.LoadInt32(&configReloadInProgress) == 1 {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		startTime := time.Now()

		select {
		case req := <-queue:
			if !running {
				return
			}
			fmt.Printf("[Worker %d] New request: %+v\n", id, req)

			rtuBuffer := pool.Get().([]byte)
			defer pool.Put(rtuBuffer)

			rtuLen := len(req.Data)
			if req.Type == TCP_REQ {
				rtuLen -= 6
				copy(rtuBuffer, req.Data[6:])
				crc := calculateCRC(rtuBuffer[:rtuLen])
				binary.LittleEndian.PutUint16(rtuBuffer[rtuLen:], crc)
				rtuLen += 2
			} else {
				copy(rtuBuffer, req.Data)
			}

			//slaveMutex.Lock()
			//flushSerial(slaveFd)
			if _, err := slaveFd.Write(rtuBuffer[:rtuLen]); err != nil {
				log.Printf("[Worker %d] Failed to write to slave: %v", id, err)
				//slaveMutex.Unlock()
				continue
			}

			deadline := time.Now().Add(timeout)
			resp, err := readSlaveResponse(slaveFd, deadline, rtuBuffer[0], rtuBuffer[1])
			//slaveMutex.Unlock()

			if err != nil {
				log.Printf("[Worker %d] Failed to read slave response: %v", id, err)
				continue
			}

			respLen := len(resp)
			if respLen == 0 {
				log.Printf("[Worker %d] Request timeout, no response received", id)
				continue
			}

			if req.Type == TCP_REQ {
				respData := make([]byte, 6+respLen-2)
				copy(respData, req.Data[:6])
				copy(respData[6:], resp[:respLen-2])
				respData[4] = 0
				respData[5] = byte(respLen-2)
				req.ClientSock.Write(respData)
			} else {
				masterFd.Write(resp)
			}

			elapsed := time.Since(startTime).Seconds() * 1000
			if elapsed < minElapsed {
				minElapsed = elapsed
				fmt.Printf("[Worker %d] New minimum elapsed time: %.2f ms\n", id, minElapsed)
			}
			if elapsed > 10 {
				fmt.Printf("[Worker %d] Request type=%d processed in %.2f ms\n", id, req.Type, elapsed)
			}
		case <-time.After(timeout):
			continue
		}
	}
	if minElapsed != float64(1<<63-1) {
		fmt.Printf("[Worker %d] Final minimum elapsed time: %.2f ms\n", id, minElapsed)
	}
}

// rtuListener 处理RTU请求
func rtuListener() {
	defer wg.Done()
	buffer := pool.Get().([]byte)
	defer pool.Put(buffer)

	configManager.configLock.RLock()
	timeout := time.Duration(configManager.currentConfig.Serial.TimeoutMs) * time.Millisecond
	configManager.configLock.RUnlock()

	for running {
		deadline := time.Now().Add(timeout)
		msg, err := readMasterRequest(masterFd, deadline)
		if err != nil {
			if err.Error() != "timeout reading master request" {
				log.Printf("RTU read error: %v", err)
			}
			time.Sleep(10 * time.Millisecond)
			continue
		}

		req := Request{
			Type: RTU_REQ,
			Data: msg,
		}
		fmt.Printf("RTU request: %x\n", req.Data)
		select {
		case queue <- req:
		default:
			log.Println("Queue full, dropping RTU request")
		}
	}
}

// tcpListener 处理TCP请求
func tcpListener() {
	defer wg.Done()
	for running {
		conn, err := serverSock.Accept()
		if err != nil {
			if running {
				log.Printf("Accept error: %v", err)
			}
			return
		}
		go func(conn net.Conn) {
			defer conn.Close()
			buffer := pool.Get().([]byte)
			defer pool.Put(buffer)
			for running {
				n, err := conn.Read(buffer)
				if err != nil {
					return
				}
				req := Request{
					Type:       TCP_REQ,
					ClientSock: conn,
					Data:       make([]byte, n),
				}
				copy(req.Data, buffer[:n])
				select {
				case queue <- req:
				default:
					log.Println("Queue full, dropping TCP request")
				}
			}
		}(conn)
	}
}

// initTCPServer 初始化TCP服务器
func initTCPServer(port int) (net.Listener, error) {
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return nil, err
	}
	return listener, nil
}

// initConfigManager 初始化配置管理器
func initConfigManager() {
	configManager = &SerialConfigManager{
		currentConfig: config,
		reloadChan:    make(chan struct{}, 1),
	}
}

// is32BitSystem 判断是否为 32 位系统
func is32BitSystem() bool {
	return runtime.GOARCH == "arm" || runtime.GOARCH == "386"
}

// handleConfigRequest 处理配置读写请求 (1503端口)
func handleConfigRequest(conn net.Conn) {
	defer conn.Close()
	buffer := make([]byte, 256)

	for {
		n, err := conn.Read(buffer)
		if err != nil {
			return
		}

		if n < 8 || (buffer[7] != 0x03 && buffer[7] != 0x06 && buffer[7] != 0x10) {
			continue
		}

		startAddr := int(binary.BigEndian.Uint16(buffer[8:10]))
		switch buffer[7] {
		case 0x03: // Read Holding Registers
			regCount := int(binary.BigEndian.Uint16(buffer[10:12]))
			if regCount <= 0 || startAddr+regCount > 8 {
				continue
			}

			configManager.configLock.RLock()
			cfg := configManager.currentConfig
			configManager.configLock.RUnlock()

			byteCount := regCount * 2
			resp := make([]byte, 9+byteCount)
			copy(resp[0:6], buffer[0:6])
			resp[4] = 0
			resp[5] = byte(3 + byteCount)
			resp[6] = buffer[6]
			resp[7] = 0x03
			resp[8] = byte(byteCount)

			for i := 0; i < regCount; i++ {
				addr := startAddr + i
				var value uint16
				switch addr {
				case 0:
					value = uint16(cfg.Serial.BaudRate >> 16)
				case 1:
					value = uint16(cfg.Serial.BaudRate & 0xFFFF)
				case 2:
					value = uint16(cfg.Serial.DataBits)
				case 3:
					switch cfg.Serial.Parity {
					case "N":
						value = 0
					case "E":
						value = 1
					case "O":
						value = 2
					}
				case 4:
					value = uint16(cfg.Serial.StopBits)
				case 5:
					value = uint16(cfg.Serial.TimeoutMs)
				case 6:
					if len(cfg.Serial.MasterDevice) >= 9 {
						if suffix, err := fmt.Sscanf(cfg.Serial.MasterDevice, "/dev/ttyS%d", &value); err == nil && suffix == 1 {
							break
						}
						if suffix, err := fmt.Sscanf(cfg.Serial.MasterDevice, "/dev/ttyAS%d", &value); err == nil && suffix == 1 {
							break
						}
					}
					value = 3
				case 7:
					if len(cfg.Serial.SlaveDevice) >= 9 {
						if suffix, err := fmt.Sscanf(cfg.Serial.SlaveDevice, "/dev/ttyS%d", &value); err == nil && suffix == 1 {
							break
						}
						if suffix, err := fmt.Sscanf(cfg.Serial.SlaveDevice, "/dev/ttyAS%d", &value); err == nil && suffix == 1 {
							break
						}
					}
					value = 4
				}
				binary.BigEndian.PutUint16(resp[9+i*2:11+i*2], value)
			}
			conn.Write(resp)

		case 0x06: // Write Single Register
			if n < 12 {
				continue
			}
			configManager.configLock.Lock()
			newConfig := configManager.currentConfig
			value := binary.BigEndian.Uint16(buffer[10:12])
			updateSingleConfig(&newConfig, startAddr, value)

			if err := saveConfig(configFilePath, newConfig); err != nil {
				log.Printf("Failed to save config to %s: %v", configFilePath, err)
			}
			configManager.currentConfig = newConfig
			configManager.configLock.Unlock()

			resp := make([]byte, 12)
			copy(resp, buffer[:12])
			conn.Write(resp)

			select {
			case configManager.reloadChan <- struct{}{}:
			default:
			}

		case 0x10: // Write Multiple Registers
			regsCount := int(binary.BigEndian.Uint16(buffer[10:12]))
			byteCount := int(buffer[12])
			if n < 13+byteCount {
				continue
			}

			configManager.configLock.Lock()
			newConfig := configManager.currentConfig
			data := buffer[13 : 13+byteCount]
			updateMultipleConfig(&newConfig, startAddr, data, regsCount)

			if err := saveConfig(configFilePath, newConfig); err != nil {
				log.Printf("Failed to save config to %s: %v", configFilePath, err)
			}
			configManager.currentConfig = newConfig
			configManager.configLock.Unlock()

			resp := make([]byte, 12)
			copy(resp[0:6], buffer[0:6])
			resp[4] = 0
			resp[5] = 6
			resp[6] = buffer[6]
			resp[7] = 0x10
			binary.BigEndian.PutUint16(resp[8:10], uint16(startAddr))
			binary.BigEndian.PutUint16(resp[10:12], uint16(regsCount))
			conn.Write(resp)

			select {
			case configManager.reloadChan <- struct{}{}:
			default:
			}
		}
	}
}

// updateSingleConfig 更新单个寄存器配置
func updateSingleConfig(cfg *Config, addr int, value uint16) {
	switch addr {
	case 0:
		cfg.Serial.BaudRate = (cfg.Serial.BaudRate & 0xFFFF) | (uint32(value) << 16)
	case 1:
		cfg.Serial.BaudRate = (cfg.Serial.BaudRate & 0xFFFF0000) | uint32(value)
	case 2:
		cfg.Serial.DataBits = int(value)
	case 3:
		switch value {
		case 0:
			cfg.Serial.Parity = "N"
		case 1:
			cfg.Serial.Parity = "E"
		case 2:
			cfg.Serial.Parity = "O"
		}
	case 4:
		cfg.Serial.StopBits = int(value)
	case 5:
		cfg.Serial.TimeoutMs = int(value)
	case 6:
		cfg.Serial.MasterDevice = fmt.Sprintf("/dev/ttyS%d", value)
		// if is32BitSystem() {
		// 	cfg.Serial.MasterDevice = fmt.Sprintf("/dev/ttyAS%d", value)
		// } else {
		// 	cfg.Serial.MasterDevice = fmt.Sprintf("/dev/ttyS%d", value)
		// }
	case 7:
		cfg.Serial.SlaveDevice = fmt.Sprintf("/dev/ttyS%d", value)
		// if is32BitSystem() {
		// 	cfg.Serial.SlaveDevice = fmt.Sprintf("/dev/ttyAS%d", value)
		// } else {
		// 	cfg.Serial.SlaveDevice = fmt.Sprintf("/dev/ttyS%d", value)
		// }
	}
}

// updateMultipleConfig 更新多个寄存器配置
func updateMultipleConfig(cfg *Config, startAddr int, data []byte, regsCount int) {
	for i := 0; i < regsCount && i*2+1 < len(data); i++ {
		value := binary.BigEndian.Uint16(data[i*2 : i*2+2])
		updateSingleConfig(cfg, startAddr+i, value)
	}
}

// saveConfig 保存配置到文件
func saveConfig(filename string, cfg Config) error {
	data, err := yaml.Marshal(cfg)
	if err != nil {
		return err
	}
	return os.WriteFile(filename, data, 0644)
}

// configReloader 配置重新加载
func configReloader() {
	defer wg.Done()
	for running {
		select {
		case <-configManager.reloadChan:
			atomic.StoreInt32(&configReloadInProgress, 1)

			newMasterFd, err := initSerial(configManager.currentConfig.Serial.MasterDevice)
			if err != nil {
				log.Printf("Failed to init new master: %v", err)
				continue
			}
			newSlaveFd, err := initSerial(configManager.currentConfig.Serial.SlaveDevice)
			if err != nil {
				log.Printf("Failed to init new slave: %v", err)
				newMasterFd.Close()
				continue
			}

			//slaveMutex.Lock()
			flushSerial(newSlaveFd)
			flushSerial(newMasterFd)
			oldMasterFd := masterFd
			oldSlaveFd := slaveFd
			masterFd = newMasterFd
			slaveFd = newSlaveFd
			//slaveMutex.Unlock()

			oldMasterFd.Close()
			oldSlaveFd.Close()

			atomic.StoreInt32(&configReloadInProgress, 0)
		case <-time.After(time.Second):
			continue
		}
	}
}

// configListener 配置读写端口监听 (1503)
func configListener() {
	defer wg.Done()
	configManager.configLock.RLock()
	port := configManager.currentConfig.Network.ConfigPort
	configManager.configLock.RUnlock()

	listener, err := initTCPServer(port)
	if err != nil {
		log.Printf("Failed to start config listener: %v", err)
		return
	}
	configSock = listener
	defer configSock.Close()

	for running {
		conn, err := configSock.Accept()
		if err != nil {
			if running {
				log.Printf("Config accept error: %v", err)
			}
			return
		}
		go handleConfigRequest(conn)
	}
}

// main 主函数
func main() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	if err := loadConfig(); err != nil {
		log.Fatal(err)
	}

	initConfigManager()

	var err error
	masterFd, err = initSerial(config.Serial.MasterDevice)
	if err != nil {
		log.Fatal(err)
	}
	defer masterFd.Close()

	slaveFd, err = initSerial(config.Serial.SlaveDevice)
	if err != nil {
		log.Fatal(err)
	}
	defer slaveFd.Close()

	serverSock, err = initTCPServer(config.Network.TCPPort)
	if err != nil {
		log.Fatal(err)
	}
	defer serverSock.Close()

	queue = make(chan Request, config.Runtime.QueueSize)
	pool = sync.Pool{New: func() interface{} { return make([]byte, config.Modbus.MaxADULength) }}

	wg.Add(5) // 固定为1个worker + tcpListener + rtuListener + configListener + configReloader
	go worker(0)
	go tcpListener()
	go rtuListener()
	go configListener()
	go configReloader()

	<-sigChan
	fmt.Println("\nReceived termination signal. Exiting...")
	running = false
	serverSock.Close()
	if configSock != nil {
		configSock.Close()
	}
	close(queue)
	wg.Wait()
}