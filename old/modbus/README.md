# modbus-gateway
Build：

gcc -o modbus-gateway modbus-gateway.c -I/usr/local/include -L/usr/local/lib -lmodbus -lpthread

gcc -o modbus-gateway modbus-gateway.c -lpthread

go build -o  modbus-gateway  modbus-gateway.go


Test：

.\modpoll.exe -m tcp -p 1502 -r 1 -c 5 -a 2 192.168.31.213

.\modpoll.exe -m rtu -b 9600 -d 8 -p none -s 1 -a 1 -r 1 -c 10 COM6

.\modpoll.exe -m tcp -p 1502 -r 1 -c 100 -a 2 -o 1 192.168.31.165



编译和替换二进制文件：

sudo -i


cd /home/<USER>/gateway/


go build -o modbus-gateway modbus-gateway.go 


supervisorctl stop modbus-gateway


cp modbus-gateway /usr/local/bin/


supervisorctl start modbus-gateway


自启动配置：

sudo -i


cd /home/<USER>/gateway/


sudo cp modbus-gateway /usr/local/bin/


sudo chmod +x /usr/local/bin/modbus-gateway


sudo mkdir -p /etc/modbus-gateway


sudo cp config.yaml /etc/modbus-gateway/config.yaml



sudo mkdir -p /var/log/


sudo touch /var/log/modbus-gateway.stdout.log /var/log/modbus-gateway.stderr.log


sudo chmod 644 /var/log/modbus-gateway.*



sudo apt update


sudo apt install -y supervisor


sudo cp modbus-gateway.conf /etc/supervisor/conf.d/modbus-gateway.conf


sudo supervisorctl reread


sudo supervisorctl update


sudo supervisorctl start modbus-gateway


sudo systemctl enable supervisor


sudo systemctl start supervisor


tronlong:

go env -w GOPROXY=https://goproxy.cn,direct
GOOS=linux GOARCH=arm GOARM=6 go build -o modbus-gateway modbus-gateway.go
scp ./modbus-gateway root@192.168.31.165:/home/<USER>

