#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <signal.h>
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>
#include <pthread.h>
#include <termios.h>
#include <stdint.h>
#include <arpa/inet.h>
#include <sys/time.h>
#include <sys/epoll.h>

#define MASTER_DEV "/dev/ttyS3"
#define SLAVE_DEV "/dev/ttyS4"
#define BAUD_RATE 9600
#define MODBUS_RTU_MAX_ADU_LENGTH 256

#define TCP_PORT 1502
#define MAX_CLIENTS 5
#define QUEUE_SIZE 500
#define WORKER_THREADS 1

pthread_mutex_t slave_lock = PTHREAD_MUTEX_INITIALIZER;

static volatile int running = 1;

typedef enum { TCP_REQ, RTU_REQ } RequestType;

typedef struct {
    RequestType type;
    int client_sock;
    uint8_t data[MODBUS_RTU_MAX_ADU_LENGTH];
    int data_len;
} Request;

// 环形队列
typedef struct {
    Request requests[QUEUE_SIZE];
    int front, rear, size;
    pthread_mutex_t lock;
    pthread_cond_t not_empty;
} RequestQueue;

RequestQueue queue = {{0}, 0, 0, 0, PTHREAD_MUTEX_INITIALIZER, PTHREAD_COND_INITIALIZER};

int master_fd, slave_fd, server_sock, epoll_fd;

// 信号处理
void signal_handler(int sig) {
    if (sig == SIGINT || sig == SIGTERM) {
        printf("\nReceived termination signal. Exiting...\n");
        running = 0;
        pthread_cond_broadcast(&queue.not_empty);
        close(server_sock);
        close(epoll_fd);
    }
}

// CRC 校验
uint16_t calculate_crc(const uint8_t *buffer, int length) {
    uint16_t crc = 0xFFFF;
    for (int i = 0; i < length; i++) {
        crc ^= buffer[i];
        for (int j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc >>= 1;
                crc ^= 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    return crc;
}

// 串口初始化
int init_serial(const char *device) {
    int fd = open(device, O_RDWR | O_NOCTTY | O_NDELAY);
    if (fd == -1) {
        perror("Failed to open serial port");
        return -1;
    }

    fcntl(fd, F_SETFL, O_NONBLOCK);
    struct termios options;
    tcgetattr(fd, &options);
    cfsetispeed(&options, B9600);
    cfsetospeed(&options, B9600);

    options.c_cflag |= (CLOCAL | CREAD);
    options.c_cflag &= ~PARENB;
    options.c_cflag &= ~CSTOPB;
    options.c_cflag &= ~CSIZE;
    options.c_cflag |= CS8;
    options.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    options.c_iflag &= ~(IXON | IXOFF | IXANY);
    options.c_oflag &= ~OPOST;

    tcsetattr(fd, TCSANOW, &options);
    tcflush(fd, TCIOFLUSH);

    return fd;
}

// 环形队列操作
int enqueue(Request req) {
    pthread_mutex_lock(&queue.lock);
    if (queue.size == QUEUE_SIZE) {
        pthread_mutex_unlock(&queue.lock);
        printf("Queue Full! Dropping request.\n");
        return -1;
    }
    queue.requests[queue.rear] = req;
    queue.rear = (queue.rear + 1) % QUEUE_SIZE;
    queue.size++;
    pthread_cond_signal(&queue.not_empty);
    pthread_mutex_unlock(&queue.lock);
    return 0;
}

Request dequeue() {
    pthread_mutex_lock(&queue.lock);
    while (queue.size == 0 && running) {
        pthread_cond_wait(&queue.not_empty, &queue.lock);
    }

    if (!running) {
        pthread_mutex_unlock(&queue.lock);
        return (Request){0};
    }

    Request req = queue.requests[queue.front];
    queue.front = (queue.front + 1) % QUEUE_SIZE;
    queue.size--;
    pthread_mutex_unlock(&queue.lock);

    return req;
}

void *worker_thread(void *arg) {
    while (running) {
        struct timeval start_time, end_time;
        gettimeofday(&start_time, NULL);  // 记录开始时间

        Request req = dequeue();
        if (!running) break;

        printf("[DEBUG] Received request: type=%d, data_len=%d\n", req.type, req.data_len);

        uint8_t rtu_buffer[MODBUS_RTU_MAX_ADU_LENGTH];
        int rtu_len = (req.type == TCP_REQ) ? req.data_len - 6 : req.data_len;
        memcpy(rtu_buffer, req.data + (req.type == TCP_REQ ? 6 : 0), rtu_len);

        // 计算 CRC 并添加到数据包
        uint16_t crc = calculate_crc(rtu_buffer, rtu_len);
        rtu_buffer[rtu_len++] = crc & 0xFF;
        rtu_buffer[rtu_len++] = (crc >> 8) & 0xFF;

       // pthread_mutex_lock(&slave_lock);
        tcflush(slave_fd, TCIFLUSH);

        if (write(slave_fd, rtu_buffer, rtu_len) < 0) {
            perror("[ERROR] Failed to write to slave");
            pthread_mutex_unlock(&slave_lock);
            continue;
        }

        struct epoll_event ev, events[1];
        ev.events = EPOLLIN;
        ev.data.fd = slave_fd;
        epoll_ctl(epoll_fd, EPOLL_CTL_ADD, slave_fd, &ev);

        int nfds = epoll_wait(epoll_fd, events, 1, 500);
        int resp_len = 0;

        if (nfds > 0) {
            while (resp_len < 5) {
                int bytes_read = read(slave_fd, rtu_buffer + resp_len, sizeof(rtu_buffer) - resp_len);
                if (bytes_read > 0) {
                    resp_len += bytes_read;
                } else {
                    perror("[ERROR] Failed to read from slave");
                    break;
                }
            }

            if (resp_len >= 5) {
                int expected_len = rtu_buffer[2] + 5;
                while (resp_len < expected_len) {
                    int bytes_read = read(slave_fd, rtu_buffer + resp_len, expected_len - resp_len);
                    if (bytes_read > 0) {
                        resp_len += bytes_read;
                    } else {
                        break;
                    }
                }
            }

            if (resp_len > 0) {
                if (req.type == TCP_REQ) {
                    memcpy(req.data + 6, rtu_buffer, resp_len - 2);
                    req.data[4] = 0;
                    req.data[5] = resp_len - 2;
                    send(req.client_sock, req.data, resp_len - 2 + 6, 0);
                } else {
                    write(master_fd, rtu_buffer, resp_len);
                }
            }
        }

      //  pthread_mutex_unlock(&slave_lock);

        gettimeofday(&end_time, NULL);  // 记录结束时间

        // 计算时间差（毫秒）
        double elapsed_time = (end_time.tv_sec - start_time.tv_sec) * 1000.0 +
                              (end_time.tv_usec - start_time.tv_usec) / 1000.0;

        printf("[PERFORMANCE] Request type=%d processed in %.2f ms\n", req.type, elapsed_time);
    }
    return NULL;
}

// RTU 监听线程
void *rtu_listener(void *arg) {
    while (running) {
        uint8_t buffer[MODBUS_RTU_MAX_ADU_LENGTH];
        int len = read(master_fd, buffer, sizeof(buffer));
        if (len > 0) {
            Request req = {RTU_REQ, -1, {0}, len};
            memcpy(req.data, buffer, len);
            enqueue(req);
        }
    }
    return NULL;
}

// TCP 监听线程
void *tcp_listener(void *arg) {
    struct epoll_event ev, events[MAX_CLIENTS];
    ev.events = EPOLLIN;
    ev.data.fd = server_sock;
    epoll_ctl(epoll_fd, EPOLL_CTL_ADD, server_sock, &ev);

    while (running) {
        int nfds = epoll_wait(epoll_fd, events, MAX_CLIENTS, -1);
        for (int i = 0; i < nfds; i++) {
            if (events[i].data.fd == server_sock) {
                int client_sock = accept(server_sock, NULL, NULL);
                ev.events = EPOLLIN;
                ev.data.fd = client_sock;
                epoll_ctl(epoll_fd, EPOLL_CTL_ADD, client_sock, &ev);
            } else {
                uint8_t buffer[MODBUS_RTU_MAX_ADU_LENGTH];
                int len = recv(events[i].data.fd, buffer, sizeof(buffer), 0);
                if (len > 0) {
                    Request req = {TCP_REQ, events[i].data.fd, {0}, len};
                    memcpy(req.data, buffer, len);
                    enqueue(req);
                }
            }
        }
    }
    return NULL;
}

// TCP 服务器初始化
int init_tcp_server() {
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    struct sockaddr_in addr = {AF_INET, htons(TCP_PORT), INADDR_ANY};
    bind(sock, (struct sockaddr *)&addr, sizeof(addr));
    listen(sock, MAX_CLIENTS);
    return sock;
}

int main() {
    signal(SIGINT, signal_handler);
    master_fd = init_serial(MASTER_DEV);
    slave_fd = init_serial(SLAVE_DEV);
    server_sock = init_tcp_server();
    epoll_fd = epoll_create1(0);

    int flags = fcntl(slave_fd, F_GETFL, 0);
    if (flags & O_NONBLOCK) {
       printf("[DEBUG] slave_fd is in NONBLOCK mode, changing to BLOCKING mode.\n");
       fcntl(slave_fd, F_SETFL, flags & ~O_NONBLOCK);
    }

    pthread_t workers[WORKER_THREADS], tcp_thread, rtu_thread;
    for (int i = 0; i < WORKER_THREADS; i++)
        pthread_create(&workers[i], NULL, worker_thread, NULL);
    pthread_create(&tcp_thread, NULL, tcp_listener, NULL);
    pthread_create(&rtu_thread, NULL, rtu_listener, NULL);

    pthread_join(tcp_thread, NULL);
    pthread_join(rtu_thread, NULL);
    return 0;
}


