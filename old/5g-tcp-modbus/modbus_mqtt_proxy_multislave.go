package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"net"
	"os"
	"sync"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"gopkg.in/yaml.v2"
)

// Config 定义中转服务器配置文件结构
type Config struct {
	TCPPort       int             `yaml:"tcp_port"`
	MqttBroker    string          `yaml:"mqtt_broker"`
	MqttUsername  string          `yaml:"mqtt_username"`
	MqttPassword  string          `yaml:"mqtt_password"`
	DeviceMapping []DeviceMapping `yaml:"device_mapping"`
}

// DeviceMapping 定义 Modbus 地址范围到 RK3566 设备ID的映射
type DeviceMapping struct {
	MinSlaveAddr byte   `yaml:"min_slave_addr"`
	MaxSlaveAddr byte   `yaml:"max_slave_addr"`
	DeviceID     string `yaml:"device_id"`
}

// ProxyServer 中转服务器结构
type ProxyServer struct {
	listener      net.Listener
	mqttClient    mqtt.Client
	masterConn    map[net.Conn]string // Master 连接到目标设备ID的映射
	deviceMapping []DeviceMapping     // Modbus 地址范围到设备ID的映射
	mutex         sync.RWMutex
	config        Config
}

// loadConfig 加载配置文件
func loadConfig(filePath string) (Config, error) {
	var config Config
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return config, fmt.Errorf("failed to read config file %s: %v", filePath, err)
	}
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return config, fmt.Errorf("failed to parse config file %s: %v", filePath, err)
	}
	log.Printf("Loaded config from %s", filePath)
	return config, nil
}

// NewProxyServer 创建新的中转服务器
func NewProxyServer(config Config) (*ProxyServer, error) {
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", config.TCPPort))
	if err != nil {
		return nil, fmt.Errorf("failed to start TCP server: %v", err)
	}

	// 初始化 MQTT 客户端，设置用户名和密码
	opts := mqtt.NewClientOptions().
		AddBroker(config.MqttBroker).
		SetClientID("proxy_server").
		SetUsername(config.MqttUsername).
		SetPassword(config.MqttPassword)
	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		return nil, fmt.Errorf("failed to connect to MQTT broker: %v", token.Error())
	}

	return &ProxyServer{
		listener:      listener,
		mqttClient:    client,
		masterConn:    make(map[net.Conn]string),
		deviceMapping: config.DeviceMapping,
		config:        config,
	}, nil
}

// Start 启动服务器
func (ps *ProxyServer) Start() {
	log.Println("Starting proxy server...")

	// 订阅所有设备的响应主题
	if token := ps.mqttClient.Subscribe("device/+/response", 1, ps.handleDeviceResponse); token.Wait() && token.Error() != nil {
		log.Fatalf("Failed to subscribe to response topics: %v", token.Error())
	}

	// 监听 TCP 连接
	for {
		conn, err := ps.listener.Accept()
		if err != nil {
			log.Printf("Accept error: %v", err)
			continue
		}
		go ps.HandleMasterConnection(conn)
	}
}

// getDeviceIDBySlaveAddr 根据 Slave 地址查找对应的设备ID
func (ps *ProxyServer) getDeviceIDBySlaveAddr(slaveAddr byte) (string, bool) {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	for _, mapping := range ps.deviceMapping {
		if slaveAddr >= mapping.MinSlaveAddr && slaveAddr <= mapping.MaxSlaveAddr {
			return mapping.DeviceID, true
		}
	}
	return "", false
}

// HandleMasterConnection 处理 Modbus Master 连接
func (ps *ProxyServer) HandleMasterConnection(conn net.Conn) {
	defer conn.Close()
	log.Println("New Modbus Master connected")
	buffer := make([]byte, 1024)

	for {
		n, err := conn.Read(buffer)
		if err != nil {
			log.Printf("Master connection error: %v", err)
			ps.mutex.Lock()
			delete(ps.masterConn, conn)
			ps.mutex.Unlock()
			return
		}

		// 解析 Modbus TCP 数据包，提取目标 Slave 地址
		if n < 7 {
			log.Println("Invalid Modbus TCP packet")
			continue
		}
		slaveAddr := buffer[6] // Modbus Slave 地址在 MBAP Header 后的第一个字节

		// 根据 Slave 地址映射到设备ID
		deviceID, ok := ps.getDeviceIDBySlaveAddr(slaveAddr)
		if !ok {
			log.Printf("No device mapped for Modbus Slave address %d", slaveAddr)
			continue
		}

		// 记录 Master 连接与设备ID的关联
		ps.mutex.Lock()
		ps.masterConn[conn] = deviceID
		ps.mutex.Unlock()

		// 发布请求到设备的 MQTT 主题
		topic := fmt.Sprintf("device/%s/request", deviceID)
		if token := ps.mqttClient.Publish(topic, 1, false, buffer[:n]); token.Wait() && token.Error() != nil {
			log.Printf("Failed to publish to MQTT topic %s: %v", topic, token.Error())
		}
		log.Printf("Forwarded request for Slave %d to device %s", slaveAddr, deviceID)
	}
}

// handleDeviceResponse 处理设备响应
func (ps *ProxyServer) handleDeviceResponse(client mqtt.Client, msg mqtt.Message) {
	topic := msg.Topic()
	deviceID := topic[len("device/") : len(topic)-len("/response")]
	payload := msg.Payload()

	log.Printf("Received response from device %s", deviceID)

	// 查找与该设备ID关联的 Master 连接
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	for conn, targetID := range ps.masterConn {
		if targetID == deviceID {
			_, err := conn.Write(payload)
			if err != nil {
				log.Printf("Failed to forward response to Master: %v", err)
				conn.Close()
				delete(ps.masterConn, conn)
			}
		}
	}
}

func main() {
	configFile := "config_proxy.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	config, err := loadConfig(configFile)
	if err != nil {
		log.Fatal(err)
	}

	server, err := NewProxyServer(config)
	if err != nil {
		log.Fatal(err)
	}
	server.Start()
}
