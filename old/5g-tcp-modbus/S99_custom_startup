#!/bin/sh
# /etc/init.d/S99_custom_startup

case "$1" in
start)
    echo "Starting custom startup script..."
    
    # 命令 1: 激活 USB0 接口
    ifconfig usb0 up
    
    # 命令 2: 发送 AT 命令到 /dev/ttyUSB1
    echo -e "AT^NDISDUP=1,1\r\n" > /dev/ttyUSB1
    
    # 命令 3: 使用 dhclient 获取 IP 地址
    dhclient usb0

    # ...... 4: ...... modbusgateway ..... ......
#    nohup /root/wp/modbus_gateway_rk3506_mqtt_multislave /root/wp/config_rk3506.yaml > /dev/null 2>&1 &
    #/root/wp/modbus_gateway_rk3506_mqtt_multislave /root/wp/config_rk3506.yaml &
   
    echo "Custom startup complete."
    ;;
stop)
    # 如果需要停止脚本，可以添加相关命令
    ;;
*)
    echo "Usage: $0 {start|stop}"
    exit 1
    ;;
esac

exit 0
