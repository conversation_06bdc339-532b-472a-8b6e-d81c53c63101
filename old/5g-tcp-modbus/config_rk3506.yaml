serial:
  master_device: "/dev/ttyUSB3"
  slave_device: "/dev/ttyS2"
  baud_rate: 115200
  data_bits: 8
  parity: "N"
  stop_bits: 1
  timeout_ms: 1000
  mode: "RTU"
network:
  tcp_port: 1502
  config_port: 1503
  max_clients: 5
  mqtt_broker: "tcp://122.51.134.87:1883"
  device_id: "rk3566_001"
  mqtt_username: "keng<PERSON>"
  mqtt_password: "Kengque@nj"
modbus:
  max_adu_length: 256
runtime:
  queue_size: 500
