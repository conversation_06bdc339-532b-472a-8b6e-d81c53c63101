#!/bin/bash

# 等待 usb0 设备上线，最多等待 60 秒
TIMEOUT=60
INTERVAL=5
ELAPSED=0

while [ $ELAPSED -lt $TIMEOUT ] && ! ip link show usb0 > /dev/null 2>&1; do
    sleep $INTERVAL
    ELAPSED=$((ELAPSED + INTERVAL))
done

if [ $ELAPSED -ge $TIMEOUT ]; then
    echo "错误: usb0 设备在 60 秒内未上线。"
    exit 1
fi

# 激活 USB0 接口
ifconfig usb0 up

# 发送 AT 命令到 /dev/ttyUSB1
echo -e "AT^NDISDUP=1,1\r\n" > /dev/ttyUSB1

# 使用 dhclient 获取 IP 地址
dhclient usb0

# 在后台运行 modbus_gateway，并忽略输出
nohup /root/wp/modbus_gateway_rk3506_mqtt_multislave /root/wp/config_rk3506.yaml > /dev/null 2>&1 &

