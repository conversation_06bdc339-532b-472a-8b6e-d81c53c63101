package main

import (
	"fmt"
	"log"
	"net"

	"github.com/spf13/viper"
)

type ClientConfig struct {
	ServerAddress string
	LogLevel      string
	LogFile       string
}

func LoadClientConfig() (*ClientConfig, error) {
	viper.SetConfigName("client_config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	config := &ClientConfig{
		ServerAddress: viper.GetString("server.address"),
		LogLevel:      viper.GetString("logging.level"),
		LogFile:       viper.GetString("logging.file"),
	}
	return config, nil
}

func main() {
	config, err := LoadClientConfig()
	if err != nil {
		log.Fatal(err)
	}

	conn, err := net.Dial("tcp", config.ServerAddress)
	if err != nil {
		log.Fatal("Failed to connect to server:", err)
	}
	defer conn.Close()

	// 发送指令
	command := []byte("get_uart_data")
	_, err = conn.Write(command)
	if err != nil {
		log.Println("Failed to send command:", err)
		return
	}

	// 接收响应
	buffer := make([]byte, 1024)
	length, err := conn.Read(buffer)
	if err != nil {
		log.Println("Failed to read response:", err)
		return
	}
	data := buffer[:length]
	fmt.Printf("Received data: %s\n", string(data))
}
