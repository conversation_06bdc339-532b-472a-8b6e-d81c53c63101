package main

import (
	"encoding/hex"
	"flag"
	"fmt"
	"log"
	"net"

	"github.com/spf13/viper"
)

type ClientConfig struct {
	ServerAddress string
	LogLevel      string
	LogFile       string
}

func LoadClientConfig(configPath string) (*ClientConfig, error) {
	viper.SetConfigName("client_config")
	viper.SetConfigType("yaml")

	// 如果提供了配置文件路径，优先使用
	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		// 否则使用默认路径
		viper.AddConfigPath(".")
	}

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	config := &ClientConfig{
		ServerAddress: viper.GetString("server.address"),
		LogLevel:      viper.GetString("logging.level"),
		LogFile:       viper.GetString("logging.file"),
	}
	return config, nil
}

func main() {
	// 定义命令行参数，允许用户指定配置文件路径和指令
	configPath := flag.String("config", "", "Path to configuration file (e.g., /path/to/client_config.yaml)")
	commandHex := flag.String("command", "01020304", "Hexadecimal command to send (e.g., 01020304)")
	flag.Parse()

	// 将十六进制字符串转换为二进制数据
	command, err := hex.DecodeString(*commandHex)
	if err != nil {
		log.Fatal("Failed to parse hexadecimal command:", err)
	}

	config, err := LoadClientConfig(*configPath)
	if err != nil {
		log.Fatal(err)
	}

	conn, err := net.Dial("tcp", config.ServerAddress)
	if err != nil {
		log.Fatal("Failed to connect to server:", err)
	}
	defer conn.Close()

	// 发送指令（二进制数据）
	_, err = conn.Write(command)
	if err != nil {
		log.Println("Failed to send command:", err)
		return
	}
	fmt.Printf("Sent command to server (hex): % X\n", command)

	// 接收响应
	buffer := make([]byte, 1024)
	length, err := conn.Read(buffer)
	if err != nil {
		log.Println("Failed to read response:", err)
		return
	}
	data := buffer[:length]
	fmt.Printf("Received data from server (hex): % X\n", data)
}
