server:
  address: "*************:504"  # 中转服务器地址
  reconnect_interval: 5      # 重连间隔时间（秒）
uart:
  port: "/dev/ttyS4"         # 串口路径
  baud: 115200                 # 波特率
  read_timeout: 5            # 读取超时时间（秒）
  data_bits: 8               # 数据位，常见值为 5,6,7,8
  stop_bits: 1               # 停止位，常见值为 1,2
  parity: "none"             # 奇偶校验，可选值: none, odd, even, mark, space
logging:
  level: "info"              # 日志级别
  file: "device.log"         # 日志文件路径
