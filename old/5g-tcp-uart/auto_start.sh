#!/bin/bash

sleep 15

# 脚本描述：Lubancat-2 上电自启动脚本（已优化为非阻塞）
echo "Starting auto-start script for Lubancat-2..."

# 命令1: 添加新的 USB 串口 ID
if echo "3466 3301" > /sys/bus/usb-serial/drivers/option1/new_id; then
    echo "Successfully added new ID."
else
    echo "Failed to add new ID." >&2
fi

# 命令2: 加载 USB 串口模块
if modprobe usbserial; then
    echo "Successfully loaded usbserial module."
else
    echo "Failed to load usbserial module." >&2
fi

# 命令3: 发送 AT 命令到 ttyUSB1（添加超时，避免阻塞）
if timeout 5 echo -e "AT^NDISDUP=1,1\r\n" > /dev/ttyUSB1; then
    echo "Successfully sent AT command."
else
    echo "Failed to send AT command or timed out." >&2
fi

# 命令4: 运行自定义程序（在后台运行，避免阻塞）
nohup /home/<USER>/wp/5g-tcp-uart/device -config /home/<USER>/wp/5g-tcp-uart/device_config.yaml > /home/<USER>/wp/5g-tcp-uart/device.log 2>&1 &  # 后台运行，并将输出重定向到日志文件
if [ $? -eq 0 ]; then  # 检查 nohup 命令是否成功启动
    echo "Custom program started in background. Output logged to /home/<USER>/wp/device.log."
else
    echo "Failed to start custom program." >&2
fi

echo "Auto-start script completed. Background tasks are running."

# 结束脚本，确保不会等待后台任务
