package main

import (
	"flag"
	"fmt"
	"log"
	"net"
	"time"

	"github.com/spf13/viper"
	"github.com/tarm/serial"
)

type DeviceConfig struct {
	ServerAddress      string
	ReconnectInterval  int
	UARTPort           string
	UARTBaud           int
	UARTReadTimeout    int
	UARTDataBits       int
	UARTStopBits       int
	UARTParity         string
	LogLevel           string
	LogFile            string
}

func LoadDeviceConfig(configPath string) (*DeviceConfig, error) {
	viper.SetConfigName("device_config")
	viper.SetConfigType("yaml")

	// 如果提供了配置文件路径，优先使用
	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		// 否则使用默认路径
		viper.AddConfigPath(".")
	}

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	config := &DeviceConfig{
		ServerAddress:     viper.GetString("server.address"),
		ReconnectInterval: viper.GetInt("server.reconnect_interval"),
		UARTPort:          viper.GetString("uart.port"),
		UARTBaud:          viper.GetInt("uart.baud"),
		UARTReadTimeout:   viper.GetInt("uart.read_timeout"),
		UARTDataBits:      viper.GetInt("uart.data_bits"),
		UARTStopBits:      viper.GetInt("uart.stop_bits"),
		UARTParity:        viper.GetString("uart.parity"),
		LogLevel:          viper.GetString("logging.level"),
		LogFile:           viper.GetString("logging.file"),
	}
	return config, nil
}

// mapParity 将配置文件中的奇偶校验字符串映射到 serial.Parity 类型
func mapParity(parityStr string) serial.Parity {
	switch parityStr {
	case "none":
		return serial.ParityNone
	case "odd":
		return serial.ParityOdd
	case "even":
		return serial.ParityEven
	case "mark":
		return serial.ParityMark
	case "space":
		return serial.ParitySpace
	default:
		log.Printf("Invalid parity value: %s, using none as default", parityStr)
		return serial.ParityNone
	}
}

// writeUARTData 将数据写入 UART 串口
func writeUARTData(port *serial.Port, data []byte) error {
	_, err := port.Write(data)
	if err != nil {
		return fmt.Errorf("failed to write to UART: %v", err)
	}
	return nil
}

// readUARTData 从 UART 串口读取数据
func readUARTData(port *serial.Port) ([]byte, error) {
	buffer := make([]byte, 1024)
	n, err := port.Read(buffer)
	if err != nil {
		return nil, fmt.Errorf("failed to read from UART: %v", err)
	}
	return buffer[:n], nil
}

func main() {
	// 定义命令行参数，允许用户指定配置文件路径
	configPath := flag.String("config", "", "Path to configuration file (e.g., /path/to/device_config.yaml)")
	flag.Parse()

	config, err := LoadDeviceConfig(*configPath)
	if err != nil {
		log.Fatal(err)
	}

	// 配置UART串口
	uartConfig := &serial.Config{
		Name:        config.UARTPort,
		Baud:        config.UARTBaud,
		ReadTimeout: time.Second * time.Duration(config.UARTReadTimeout),
		Size:        byte(config.UARTDataBits),             // 数据位
		StopBits:    serial.StopBits(config.UARTStopBits), // 停止位
		Parity:      mapParity(config.UARTParity),         // 奇偶校验
	}
	uartPort, err := serial.OpenPort(uartConfig)
	if err != nil {
		log.Fatal("Failed to open UART port:", err)
	}
	defer uartPort.Close()

	var conn net.Conn
	for {
		// 连接中转服务器 (通过5G网络)
		conn, err = net.Dial("tcp", config.ServerAddress)
		if err != nil {
			log.Printf("Failed to connect to server: %v, retrying in %d seconds", err, config.ReconnectInterval)
			time.Sleep(time.Second * time.Duration(config.ReconnectInterval))
			continue
		}
		fmt.Println("Connected to server:", config.ServerAddress)
		break
	}
	defer conn.Close()

	// 接收服务器指令并处理
	go func() {
		buffer := make([]byte, 1024)
		for {
			length, err := conn.Read(buffer)
			if err != nil {
				log.Println("Failed to read from server:", err)
				return
			}
			data := buffer[:length]
			fmt.Printf("Received command from server (hex): % X\n", data)

			// 将指令写入 UART 串口
			err = writeUARTData(uartPort, data)
			if err != nil {
				log.Println("Failed to write command to UART:", err)
				continue
			}
			fmt.Printf("Sent command to UART (hex): % X\n", data)

			// 采集 UART 数据（读取外部设备响应）
			uartData, err := readUARTData(uartPort)
			if err != nil {
				log.Println("Failed to read UART data:", err)
				continue
			}
			fmt.Printf("Received data from UART (hex): % X\n", uartData)

			// 将数据回传给服务器
			_, err = conn.Write(uartData)
			if err != nil {
				log.Println("Failed to send data to server:", err)
			}
		}
	}()

	// 主循环保持运行
	select {}
}
