package main

import (
	"flag"
	"fmt"
	"log"
	"net"
	"sync"

	"github.com/spf13/viper"
)

// ClientManager 管理嵌入式设备和外部客户端的连接
type ClientManager struct {
	clients    map[net.Conn]string
	deviceConn net.Conn
	mutex      sync.Mutex
}

type ServerConfig struct {
	ClientPort      int
	DevicePort      int
	MaxConnections  int
	LogLevel        string
	LogFile         string
}

func LoadServerConfig(configPath string) (*ServerConfig, error) {
	viper.SetConfigName("server_config")
	viper.SetConfigType("yaml")

	// 如果提供了配置文件路径，优先使用
	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		// 否则使用默认路径
		viper.AddConfigPath(".")
	}

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}

	config := &ServerConfig{
		ClientPort:     viper.GetInt("server.client_port"),
		DevicePort:     viper.GetInt("server.device_port"),
		MaxConnections: viper.GetInt("server.max_connections"),
		LogLevel:       viper.GetString("logging.level"),
		LogFile:        viper.GetString("logging.file"),
	}
	return config, nil
}

func NewClientManager() *ClientManager {
	return &ClientManager{
		clients: make(map[net.Conn]string),
	}
}

func (cm *ClientManager) AddClient(conn net.Conn) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.clients[conn] = conn.RemoteAddr().String()
	fmt.Printf("New client connected: %s\n", conn.RemoteAddr().String())
}

func (cm *ClientManager) RemoveClient(conn net.Conn) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	delete(cm.clients, conn)
	fmt.Printf("Client disconnected: %s\n", conn.RemoteAddr().String())
}

func (cm *ClientManager) SetDeviceConn(conn net.Conn) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	cm.deviceConn = conn
	fmt.Printf("Device connected: %s\n", conn.RemoteAddr().String())
}

func (cm *ClientManager) HandleClient(conn net.Conn) {
	defer conn.Close()
	cm.AddClient(conn)
	defer cm.RemoveClient(conn)

	buffer := make([]byte, 1024)
	for {
		length, err := conn.Read(buffer)
		if err != nil {
			return
		}
		data := buffer[:length]
		fmt.Printf("Received from client %s (hex): % X\n", conn.RemoteAddr().String(), data)

		cm.mutex.Lock()
		if cm.deviceConn != nil {
			_, err := cm.deviceConn.Write(data)
			if err != nil {
				fmt.Println("Failed to forward to device:", err)
			}
		}
		cm.mutex.Unlock()
	}
}

func (cm *ClientManager) HandleDevice(conn net.Conn) {
	defer conn.Close()
	cm.SetDeviceConn(conn)

	buffer := make([]byte, 1024)
	for {
		length, err := conn.Read(buffer)
		if err != nil {
			cm.mutex.Lock()
			cm.deviceConn = nil
			cm.mutex.Unlock()
			fmt.Println("Device disconnected")
			return
		}
		data := buffer[:length]
		fmt.Printf("Received from device (hex): % X\n", data)

		cm.mutex.Lock()
		for client := range cm.clients {
			_, err := client.Write(data)
			if err != nil {
				fmt.Println("Failed to forward to client:", err)
			}
		}
		cm.mutex.Unlock()
	}
}

func main() {
	// 定义命令行参数，允许用户指定配置文件路径
	configPath := flag.String("config", "", "Path to configuration file (e.g., /path/to/server_config.yaml)")
	flag.Parse()

	config, err := LoadServerConfig(*configPath)
	if err != nil {
		log.Fatal(err)
	}

	// 监听外部客户端连接
	clientListener, err := net.Listen("tcp", fmt.Sprintf(":%d", config.ClientPort))
	if err != nil {
		log.Fatal("Failed to listen for clients:", err)
	}
	defer clientListener.Close()

	// 监听嵌入式设备连接
	deviceListener, err := net.Listen("tcp", fmt.Sprintf(":%d", config.DevicePort))
	if err != nil {
		log.Fatal("Failed to listen for device:", err)
	}
	defer deviceListener.Close()

	cm := NewClientManager()

	// 启动goroutine接受设备连接
	go func() {
		for {
			conn, err := deviceListener.Accept()
			if err != nil {
				log.Println("Failed to accept device connection:", err)
				continue
			}
			go cm.HandleDevice(conn)
		}
	}()

	// 接受外部客户端连接
	for {
		conn, err := clientListener.Accept()
		if err != nil {
			log.Println("Failed to accept client connection:", err)
			continue
		}
		go cm.HandleClient(conn)
	}
}
