package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"net"
	"os"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"gopkg.in/yaml.v2"
)

// Config 定义中转服务器配置文件结构
type Config struct {
	TCPPort       int             `yaml:"tcp_port"`
	MqttBroker    string          `yaml:"mqtt_broker"`
	MqttUsername  string          `yaml:"mqtt_username"`
	MqttPassword  string          `yaml:"mqtt_password"`
	DeviceMapping []DeviceMapping `yaml:"device_mapping"`
}

// DeviceMapping 定义 Modbus 地址范围到 RK3566 设备ID的映射
type DeviceMapping struct {
	MinSlaveAddr byte   `yaml:"min_slave_addr"`
	MaxSlaveAddr byte   `yaml:"max_slave_addr"`
	DeviceID     string `yaml:"device_id"`
}

// ProxyServer 中转服务器结构
type ProxyServer struct {
	listener      net.Listener
	mqttClient    mqtt.Client
	masterConn    map[net.Conn]string // Master 连接到目标设备ID的映射
	deviceMapping []DeviceMapping     // Modbus 地址范围到设备ID的映射
	mutex         sync.RWMutex
	config        Config
}

// loadConfig 加载配置文件
func loadConfig(filePath string) (Config, error) {
	var config Config
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return config, fmt.Errorf("failed to read config file %s: %v", filePath, err)
	}
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return config, fmt.Errorf("failed to parse config file %s: %v", filePath, err)
	}
	log.Printf("[%s] [INFO] 配置文件加载成功: %s", time.Now().Format("2006-01-02 15:04:05"), filePath)
	log.Printf("[%s] [INFO] TCP端口: %d, MQTT代理: %s",
		time.Now().Format("2006-01-02 15:04:05"), config.TCPPort, config.MqttBroker)
	log.Printf("[%s] [INFO] 设备映射配置: %d个映射规则",
		time.Now().Format("2006-01-02 15:04:05"), len(config.DeviceMapping))
	return config, nil
}

// NewProxyServer 创建新的中转服务器
func NewProxyServer(config Config) (*ProxyServer, error) {
	log.Printf("[%s] [INFO] 正在启动MQTT代理服务器...", time.Now().Format("2006-01-02 15:04:05"))

	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", config.TCPPort))
	if err != nil {
		return nil, fmt.Errorf("failed to start TCP server: %v", err)
	}
	log.Printf("[%s] [INFO] TCP监听器启动成功，端口: %d", time.Now().Format("2006-01-02 15:04:05"), config.TCPPort)

	// 初始化 MQTT 客户端，设置用户名和密码
	log.Printf("[%s] [INFO] 正在连接MQTT代理: %s", time.Now().Format("2006-01-02 15:04:05"), config.MqttBroker)
	opts := mqtt.NewClientOptions().
		AddBroker(config.MqttBroker).
		SetClientID("proxy_server").
		SetUsername(config.MqttUsername).
		SetPassword(config.MqttPassword)
	client := mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		return nil, fmt.Errorf("failed to connect to MQTT broker: %v", token.Error())
	}
	log.Printf("[%s] [INFO] MQTT连接建立成功", time.Now().Format("2006-01-02 15:04:05"))

	return &ProxyServer{
		listener:      listener,
		mqttClient:    client,
		masterConn:    make(map[net.Conn]string),
		deviceMapping: config.DeviceMapping,
		config:        config,
	}, nil
}

// Start 启动服务器
func (ps *ProxyServer) Start() {
	log.Printf("[%s] [INFO] ====================================", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("[%s] [INFO] MQTT代理服务器启动成功", time.Now().Format("2006-01-02 15:04:05"))

	// 订阅所有设备的响应主题
	subscriptionTopic := "device/+/response"
	if token := ps.mqttClient.Subscribe(subscriptionTopic, 1, ps.handleDeviceResponse); token.Wait() && token.Error() != nil {
		log.Fatalf("Failed to subscribe to response topics: %v", token.Error())
	}
	log.Printf("[%s] [INFO] 已订阅MQTT主题: %s", time.Now().Format("2006-01-02 15:04:05"), subscriptionTopic)
	log.Printf("[%s] [INFO] 系统已进入运行状态，等待Modbus Master连接...", time.Now().Format("2006-01-02 15:04:05"))

	// 监听 TCP 连接
	for {
		conn, err := ps.listener.Accept()
		if err != nil {
			log.Printf("[%s] [ERROR] Accept error: %v", time.Now().Format("2006-01-02 15:04:05"), err)
			continue
		}
		log.Printf("[%s] [INFO] 新的Modbus Master连接: %s", time.Now().Format("2006-01-02 15:04:05"), conn.RemoteAddr())
		go ps.HandleMasterConnection(conn)
	}
}

// getDeviceIDBySlaveAddr 根据 Slave 地址查找对应的设备ID
func (ps *ProxyServer) getDeviceIDBySlaveAddr(slaveAddr byte) (string, bool) {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	for _, mapping := range ps.deviceMapping {
		if slaveAddr >= mapping.MinSlaveAddr && slaveAddr <= mapping.MaxSlaveAddr {
			return mapping.DeviceID, true
		}
	}
	return "", false
}

// HandleMasterConnection 处理 Modbus Master 连接
func (ps *ProxyServer) HandleMasterConnection(conn net.Conn) {
	defer conn.Close()
	defer func() {
		ps.mutex.Lock()
		delete(ps.masterConn, conn)
		ps.mutex.Unlock()
		log.Printf("[%s] [INFO] Modbus Master连接关闭: %s", time.Now().Format("2006-01-02 15:04:05"), conn.RemoteAddr())
	}()

	buffer := make([]byte, 1024)
	log.Printf("[%s] [INFO] 开始处理Modbus Master连接: %s", time.Now().Format("2006-01-02 15:04:05"), conn.RemoteAddr())

	for {
		n, err := conn.Read(buffer)
		if err != nil {
			log.Printf("[%s] [DEBUG] Master连接读取错误: %v (连接: %s)",
				time.Now().Format("2006-01-02 15:04:05"), err, conn.RemoteAddr())
			return
		}

		// 解析 Modbus TCP 数据包，提取目标 Slave 地址
		if n < 7 {
			log.Printf("[%s] [WARN] 收到无效的Modbus TCP数据包，长度: %d (连接: %s)",
				time.Now().Format("2006-01-02 15:04:05"), n, conn.RemoteAddr())
			continue
		}
		slaveAddr := buffer[6] // Modbus Slave 地址在 MBAP Header 后的第一个字节

		// 根据 Slave 地址映射到设备ID
		deviceID, ok := ps.getDeviceIDBySlaveAddr(slaveAddr)
		if !ok {
			log.Printf("[%s] [WARN] 未找到Slave地址 %d 对应的设备映射 (连接: %s)",
				time.Now().Format("2006-01-02 15:04:05"), slaveAddr, conn.RemoteAddr())
			continue
		}

		// 记录 Master 连接与设备ID的关联
		ps.mutex.Lock()
		ps.masterConn[conn] = deviceID
		ps.mutex.Unlock()

		// 发布请求到设备的 MQTT 主题
		topic := fmt.Sprintf("device/%s/request", deviceID)
		if token := ps.mqttClient.Publish(topic, 1, false, buffer[:n]); token.Wait() && token.Error() != nil {
			log.Printf("[%s] [ERROR] 发布MQTT请求失败，主题: %s, 错误: %v",
				time.Now().Format("2006-01-02 15:04:05"), topic, token.Error())
		} else {
			log.Printf("[%s] [DEBUG] 请求已转发: Slave地址=%d -> 设备=%s, 数据长度=%d字节",
				time.Now().Format("2006-01-02 15:04:05"), slaveAddr, deviceID, n)
		}
	}
}

// handleDeviceResponse 处理设备响应
func (ps *ProxyServer) handleDeviceResponse(client mqtt.Client, msg mqtt.Message) {
	topic := msg.Topic()
	deviceID := topic[len("device/") : len(topic)-len("/response")]
	payload := msg.Payload()

	log.Printf("[%s] [DEBUG] 收到设备响应: 设备=%s, 数据长度=%d字节",
		time.Now().Format("2006-01-02 15:04:05"), deviceID, len(payload))

	// 查找与该设备ID关联的 Master 连接
	ps.mutex.RLock()
	connCount := 0
	for conn, targetID := range ps.masterConn {
		if targetID == deviceID {
			connCount++
			_, err := conn.Write(payload)
			if err != nil {
				log.Printf("[%s] [ERROR] 转发响应到Master失败: %v (连接: %s)",
					time.Now().Format("2006-01-02 15:04:05"), err, conn.RemoteAddr())
				conn.Close()
			} else {
				log.Printf("[%s] [DEBUG] 响应已转发到Master: 设备=%s -> %s, 数据长度=%d字节",
					time.Now().Format("2006-01-02 15:04:05"), deviceID, conn.RemoteAddr(), len(payload))
			}
		}
	}
	ps.mutex.RUnlock()

	if connCount == 0 {
		log.Printf("[%s] [WARN] 设备 %s 的响应无对应的Master连接",
			time.Now().Format("2006-01-02 15:04:05"), deviceID)
	}
}

func main() {
	log.Printf("[%s] [INFO] ====================================", time.Now().Format("2006-01-02 15:04:05"))
	log.Printf("[%s] [INFO] MQTT代理服务启动", time.Now().Format("2006-01-02 15:04:05"))

	configFile := "config_proxy.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}
	log.Printf("[%s] [INFO] 使用配置文件: %s", time.Now().Format("2006-01-02 15:04:05"), configFile)

	config, err := loadConfig(configFile)
	if err != nil {
		log.Fatalf("[%s] [ERROR] 配置加载失败: %v", time.Now().Format("2006-01-02 15:04:05"), err)
	}

	server, err := NewProxyServer(config)
	if err != nil {
		log.Fatalf("[%s] [ERROR] 服务器创建失败: %v", time.Now().Format("2006-01-02 15:04:05"), err)
	}

	server.Start()
}
