{"system": {"poll_interval": 1000, "command_interval": 2000, "web_refresh_interval": 3000}, "serial": {"port": "/dev/ttyXRUSB0", "baudrate": 1200, "databits": 8, "stopbits": 1, "parity": "odd", "timeout": 1000}, "modbus": {"enabled": true, "port": 1502}, "web": {"enabled": true, "port": 8080}, "mqtt": {"enabled": true, "broker": "tcp://*************:1883", "username": "kengque", "password": "<PERSON><PERSON><PERSON>@nj", "device_id": "rk3566_002", "qos": 1}, "node": {"address": 0, "enabled": true, "commands": [{"command": 0, "description": "读取设备标识符", "enabled": true, "modbus_register": 30001, "data_type": "raw"}, {"command": 1, "description": "读取主变量", "enabled": true, "modbus_register": 30031, "data_type": "float32", "unit": "°C", "alarm_low": -10.0, "alarm_high": 100.0}, {"command": 2, "description": "读取2变量", "enabled": true, "modbus_register": 30061, "data_type": "raw"}, {"command": 3, "description": "读取多变量", "enabled": true, "modbus_register": 30091, "data_type": "raw"}, {"command": 200, "description": "自定义命令200", "enabled": true, "modbus_register": 30111, "data_type": "raw_frame", "command_type": "custom", "custom_input_data": "FF FF FF FF FF 02 80 00 00 82"}, {"command": 201, "description": "自定义命令201", "enabled": true, "modbus_register": 30131, "data_type": "raw_frame", "command_type": "custom", "custom_input_data": "FF FF FF FF FF 02 80 02 00 81"}, {"command": 202, "description": "自定义命令202", "enabled": true, "modbus_register": 30161, "data_type": "raw_frame", "command_type": "custom", "custom_input_data": "FF FF FF FF FF 02 80 03 00 82"}]}, "memory": {"total_size": 5000, "input_registers_start": 30001}}