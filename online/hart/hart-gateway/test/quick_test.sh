#!/bin/bash

# HART网关 Modbus TCP 快速测试脚本
# 用于快速启动和测试HART网关功能

echo "🚀 HART网关 Modbus TCP 快速测试脚本"
echo "=================================="

# 检查参数
if [ $# -eq 0 ]; then
    echo ""
    echo "用法:"
    echo "  $0 start              # 启动网关"
    echo "  $0 test               # 运行基本测试"
    echo "  $0 continuous         # 运行连续测试"
    echo "  $0 both               # 启动网关并运行测试"
    echo ""
    echo "示例:"
    echo "  $0 start"
    echo "  $0 test"
    echo "  $0 continuous"
    echo "  $0 both"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 错误: 未找到Go环境，请先安装Go"
    exit 1
fi

# 检查必要文件
if [ ! -f "simple_hart_gateway.go" ]; then
    echo "❌ 错误: 未找到 simple_hart_gateway.go"
    exit 1
fi

if [ ! -f "simple_config.json" ]; then
    echo "❌ 错误: 未找到 simple_config.json"
    exit 1
fi

if [ ! -f "test/main.go" ]; then
    echo "❌ 错误: 未找到测试客户端 test/main.go"
    exit 1
fi

# 函数：启动网关
start_gateway() {
    echo "🔧 启动HART网关..."
    echo "配置文件: simple_config.json"
    echo "Modbus端口: 1502"
    echo "Web端口: 8080"
    echo ""
    echo "按 Ctrl+C 停止网关"
    echo "=================================="
    
    go run simple_hart_gateway.go
}

# 函数：运行测试
run_test() {
    echo "🧪 运行Modbus TCP测试客户端..."
    echo ""
    
    # 等待用户确认网关已启动
    read -p "请确认HART网关已启动并按回车继续..." -n 1 -r
    echo ""
    
    cd test
    go run main.go test localhost 1502
    cd ..
}

# 函数：运行连续测试
run_continuous_test() {
    echo "🔄 运行连续测试..."
    echo ""
    
    # 等待用户确认网关已启动
    read -p "请确认HART网关已启动并按回车继续..." -n 1 -r
    echo ""
    
    cd test
    go run main.go continuous localhost 1502 2s 10
    cd ..
}

# 函数：启动网关并运行测试
start_both() {
    echo "🚀 启动网关并运行测试..."
    echo ""
    
    # 在后台启动网关
    echo "正在后台启动网关..."
    go run simple_hart_gateway.go &
    GATEWAY_PID=$!
    
    # 等待网关启动
    echo "等待网关启动..."
    sleep 3
    
    # 检查网关是否正在运行
    if ! kill -0 $GATEWAY_PID 2>/dev/null; then
        echo "❌ 网关启动失败"
        exit 1
    fi
    
    echo "✅ 网关已启动 (PID: $GATEWAY_PID)"
    echo ""
    
    # 运行测试
    echo "🧪 开始运行测试..."
    cd test
    go run main.go test localhost 1502
    cd ..
    
    # 停止网关
    echo ""
    echo "🛑 停止网关..."
    kill $GATEWAY_PID
    wait $GATEWAY_PID 2>/dev/null
    echo "✅ 网关已停止"
}

# 主逻辑
case "$1" in
    "start")
        start_gateway
        ;;
    "test")
        run_test
        ;;
    "continuous")
        run_continuous_test
        ;;
    "both")
        start_both
        ;;
    *)
        echo "❌ 未知参数: $1"
        echo "支持的参数: start, test, continuous, both"
        exit 1
        ;;
esac

echo ""
echo "✅ 脚本执行完成" 