package main

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"log"
	"math"
	"net"
	"os"
	"strconv"
	"time"
)

// Modbus功能码
const (
	MbReadInputRegisters = 0x04
	MbReadHoldingRegs    = 0x03
)

// 异常码
const (
	ExIllegalFunction = 0x01
	ExIllegalAddress  = 0x02
	ExIllegalValue    = 0x03
	ExSlaveFailure    = 0x04
)

// ModbusClient Modbus TCP 客户端
type ModbusClient struct {
	conn          net.Conn
	transactionID uint16
	logger        *log.Logger
}

// NewModbusClient 创建新的Modbus客户端
func NewModbusClient(host string, port int) (*ModbusClient, error) {
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}

	client := &ModbusClient{
		conn:          conn,
		transactionID: 1,
		logger:        log.New(os.Stdout, "[Modbus客户端] ", log.LstdFlags),
	}

	client.logger.Printf("成功连接到 %s:%d", host, port)
	return client, nil
}

// Close 关闭连接
func (c *ModbusClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

// ReadInputRegisters 读取输入寄存器
func (c *ModbusClient) ReadInputRegisters(startAddr, quantity uint16) ([]uint16, error) {
	// 构建请求
	data := make([]byte, 4)
	binary.BigEndian.PutUint16(data[0:2], startAddr)
	binary.BigEndian.PutUint16(data[2:4], quantity)

	request := c.buildTCPFrame(MbReadInputRegisters, data)

	c.logger.Printf("发送请求 → % X", request)

	// 发送请求
	_, err := c.conn.Write(request)
	if err != nil {
		return nil, fmt.Errorf("发送失败: %v", err)
	}

	// 读取响应
	response, err := c.readTCPFrame()
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	c.logger.Printf("接收响应 ← % X", response)

	// 解析响应
	return c.parseRegisterResponse(response, quantity)
}

// buildTCPFrame 构建Modbus TCP帧
func (c *ModbusClient) buildTCPFrame(funcCode uint8, data []byte) []byte {
	var buf bytes.Buffer

	// MBAP Header
	binary.Write(&buf, binary.BigEndian, c.getNextTransactionID()) // Transaction ID
	binary.Write(&buf, binary.BigEndian, uint16(0))                // Protocol ID
	binary.Write(&buf, binary.BigEndian, uint16(2+len(data)))      // Length
	buf.WriteByte(1)                                               // Unit ID

	// PDU
	buf.WriteByte(funcCode)
	buf.Write(data)

	return buf.Bytes()
}

// readTCPFrame 读取Modbus TCP响应
func (c *ModbusClient) readTCPFrame() ([]byte, error) {
	c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取MBAP头部
	header := make([]byte, 6)
	_, err := c.conn.Read(header)
	if err != nil {
		return nil, err
	}

	length := binary.BigEndian.Uint16(header[4:6])

	// 读取剩余数据
	remaining := make([]byte, length)
	_, err = c.conn.Read(remaining)
	if err != nil {
		return nil, err
	}

	// 组合完整响应
	response := append(header, remaining...)
	return response, nil
}

// parseRegisterResponse 解析寄存器响应
func (c *ModbusClient) parseRegisterResponse(response []byte, quantity uint16) ([]uint16, error) {
	if len(response) < 8 {
		return nil, fmt.Errorf("响应长度不足")
	}

	funcCode := response[7]

	// 检查异常响应
	if funcCode&0x80 != 0 {
		if len(response) >= 9 {
			exCode := response[8]
			return nil, fmt.Errorf("Modbus异常: 0x%02X (%s)", exCode, c.getExceptionString(exCode))
		}
		return nil, fmt.Errorf("未知异常响应")
	}

	if len(response) < 9 {
		return nil, fmt.Errorf("响应数据不足")
	}

	byteCount := response[8]
	expectedBytes := int(quantity) * 2

	if int(byteCount) != expectedBytes {
		return nil, fmt.Errorf("字节计数错误: 期望%d, 实际%d", expectedBytes, byteCount)
	}

	if len(response) < 9+int(byteCount) {
		return nil, fmt.Errorf("数据不完整")
	}

	// 解析寄存器值
	registers := make([]uint16, quantity)
	dataStart := 9

	for i := 0; i < int(quantity); i++ {
		registers[i] = binary.BigEndian.Uint16(response[dataStart+i*2 : dataStart+i*2+2])
	}

	return registers, nil
}

// getNextTransactionID 获取下一个事务ID
func (c *ModbusClient) getNextTransactionID() uint16 {
	id := c.transactionID
	c.transactionID++
	if c.transactionID == 0 {
		c.transactionID = 1
	}
	return id
}

// getExceptionString 获取异常描述
func (c *ModbusClient) getExceptionString(code uint8) string {
	switch code {
	case ExIllegalFunction:
		return "非法功能码"
	case ExIllegalAddress:
		return "非法数据地址"
	case ExIllegalValue:
		return "非法数据值"
	case ExSlaveFailure:
		return "从设备故障"
	default:
		return "未知异常"
	}
}

// TestHARTGateway 测试HART网关
func (c *ModbusClient) TestHARTGateway() {
	c.logger.Println("=== 开始测试HART网关Modbus接口 ===")

	// 基于simple_config.json配置的测试用例
	testCases := []struct {
		name  string
		addr  uint16
		count uint16
		desc  string
	}{
		{"设备标识", 30001, 10, "HART命令0 - 设备标识符"},
		{"主变量", 30021, 3, "HART命令1 - 主变量(温度)"},
		{"多变量", 30031, 12, "HART命令3 - 多变量数据"},
		{"全区域", 30001, 50, "读取完整数据区域"},
	}

	for i, test := range testCases {
		c.logger.Printf("\n--- 测试 %d: %s ---", i+1, test.name)
		c.logger.Printf("描述: %s", test.desc)
		c.logger.Printf("地址: %d, 数量: %d", test.addr, test.count)

		registers, err := c.ReadInputRegisters(test.addr, test.count)
		if err != nil {
			c.logger.Printf("❌ 失败: %v", err)
			continue
		}

		c.logger.Printf("✅ 成功读取 %d 个寄存器", len(registers))

		// 显示数据
		for j, reg := range registers {
			addr := test.addr + uint16(j)
			c.logger.Printf("  [%d]: 0x%04X (%d)", addr, reg, reg)
		}

		// 特殊处理温度数据
		if test.name == "主变量" && len(registers) >= 2 {
			var floatBytes [4]byte
			binary.BigEndian.PutUint16(floatBytes[0:2], registers[0])
			binary.BigEndian.PutUint16(floatBytes[2:4], registers[1])

			temp := math.Float32frombits(binary.BigEndian.Uint32(floatBytes[:]))
			c.logger.Printf("  解析温度值: %.2f°C", temp)
		}

		time.Sleep(1 * time.Second)
	}
}

// ContinuousTest 连续测试
func (c *ModbusClient) ContinuousTest(interval time.Duration, count int) {
	c.logger.Printf("=== 连续测试模式 ===")
	c.logger.Printf("间隔: %v, 次数: %d", interval, count)

	for i := 0; i < count; i++ {
		c.logger.Printf("\n--- 第 %d/%d 次 ---", i+1, count)

		registers, err := c.ReadInputRegisters(30021, 3)
		if err != nil {
			c.logger.Printf("❌ 失败: %v", err)
		} else {
			c.logger.Printf("✅ 成功: %v", registers)
		}

		if i < count-1 {
			time.Sleep(interval)
		}
	}
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("🔧 Modbus TCP测试客户端 - 用于测试HART网关")
		fmt.Println("")
		fmt.Println("用法:")
		fmt.Println("  go run main.go test [host] [port]")
		fmt.Println("  go run main.go continuous [host] [port] [间隔] [次数]")
		fmt.Println("")
		fmt.Println("示例:")
		fmt.Println("  go run main.go test localhost 1502")
		fmt.Println("  go run main.go continuous localhost 1502 2s 10")
		fmt.Println("")
		fmt.Println("配置:")
		fmt.Println("  默认主机: localhost")
		fmt.Println("  默认端口: 1502 (与simple_config.json一致)")
		os.Exit(1)
	}

	mode := os.Args[1]
	host := "localhost"
	port := 1502

	if len(os.Args) >= 3 {
		host = os.Args[2]
	}
	if len(os.Args) >= 4 {
		if p, err := strconv.Atoi(os.Args[3]); err == nil {
			port = p
		}
	}

	client, err := NewModbusClient(host, port)
	if err != nil {
		log.Fatalf("创建客户端失败: %v", err)
	}
	defer client.Close()

	switch mode {
	case "test":
		client.TestHARTGateway()

	case "continuous":
		interval := 2 * time.Second
		count := 10

		if len(os.Args) >= 5 {
			if d, err := time.ParseDuration(os.Args[4]); err == nil {
				interval = d
			}
		}
		if len(os.Args) >= 6 {
			if c, err := strconv.Atoi(os.Args[5]); err == nil {
				count = c
			}
		}

		client.ContinuousTest(interval, count)

	default:
		fmt.Printf("未知模式: %s\n", mode)
		fmt.Println("支持的模式: test, continuous")
		os.Exit(1)
	}

	client.logger.Println("✅ 测试完成")
}
