# HART-Modbus 网关项目开发规则

## 项目概述
这是一个 HART 到 Modbus TCP 协议转换网关项目，用 Go 语言开发，支持：
- HART 协议串口通信
- Modbus TCP 服务器
- Web 监控界面
- 自定义命令配置
- 实时数据采集和转换

## 忽略目录
忽略以下目录，它们包含旧版本或备份代码：
- `bk/`：备份代码目录
- `old/`：旧版本代码目录

## 代码风格和约定

### Go 语言规范
- 严格遵循 Go 语言官方代码规范（gofmt、golint）
- 使用驼峰命名法（CamelCase）用于导出的标识符
- 使用小写驼峰命名法（camelCase）用于非导出的标识符
- 结构体字段使用 JSON 标签进行序列化控制
- 错误处理必须显式处理，不允许忽略错误

### 命名约定
- 结构体前缀：
  - `Simple*`：简化版组件（如 SimpleConfig, SimpleGateway）
  - `HART*`：HART 协议相关（如 HARTFrame, HARTResponse）
  - `Modbus*`：Modbus 协议相关（如 ModbusRequest, ModbusResponse）
- 常量使用全大写加下划线：`SIMPLE_HART_PREAMBLE_LENGTH`
- 配置相关变量以 `config` 开头
- 日志相关变量以 `logger` 开头

### 日志规范
- 使用中文日志信息，便于现场维护人员理解
- 日志级别：
  - `[CMD%d]`：命令执行相关日志
  - `[HART解析]`：HART 协议解析日志
  - `[Modbus]`：Modbus 协议相关日志
  - `⚠️`：警告信息
  - `===`：重要状态信息
- 十六进制数据使用 `% X` 格式化

## 协议实现规范

### HART 协议
- 帧格式：前导码(5字节) + 定界符 + 地址 + 命令 + 字节数 + 数据 + 校验
- 支持点对点模式（地址 0x80）和多点模式
- 校验和使用 XOR 算法
- 支持短帧和长帧格式
- 自定义命令支持完整帧数据输入

### Modbus TCP
- 支持功能码 0x04（读取输入寄存器）
- 寄存器映射从配置文件的 `input_registers_start` 开始
- 异常处理：非法功能码、非法数据地址
- 大端字节序（Big Endian）

### 配置文件结构
- JSON 格式，极简化设计，只保留必需字段
- 移除冗余的元数据字段（version、description、logging等）
- 支持标准命令和自定义命令两种类型
- 使用 `omitempty` 标签减少配置文件体积
- 自定义命令数据格式：
  - 逗号分隔：`"0x01,0x02,0x03"`
  - 空格分隔：`"FF FF FF FF"`
  - 连续字符串：`"010203"`

## 开发约定

### 错误处理
- 串口初始化失败不应终止整个网关，改为仅 Modbus 模式运行
- 命令执行失败应记录详细错误信息，但不影响其他命令
- 网络连接错误应优雅处理，避免程序崩溃

### 并发安全
- 使用 `sync.RWMutex` 保护共享数据
- 内存数据访问必须加锁
- **串口访问必须串行化**：HART协议通过串口通信，串口不支持并发读写，必须使用互斥锁确保一写一读的原子性
- 禁止为每个命令启动独立协程，应使用串行轮询调度

### 性能考虑
- 命令轮询使用串行调度器，轮询间隔可配置
- 串口读写使用适当的超时设置
- 串口命令间需要可配置间隔，防止设备响应不过来
- Web 界面数据刷新间隔可配置

## 文件组织
- `simple_hart_gateway.go`：主程序文件
- `simple_config.json`：配置文件
- `test/`：测试代码目录
- `design/`：设计文档目录
- 部署脚本：`quick_deploy.sh`、`remote_deploy_test.sh`

## Web 界面规范
- 使用响应式设计，支持移动设备
- 颜色方案：
  - 主色调：#2c3e50（深蓝）
  - 成功状态：#27ae60（绿色）
  - 错误状态：#e74c3c（红色）
  - 警告状态：#e67e22（橙色）
- 实时数据使用等宽字体显示
- 自动刷新间隔 3 秒

## 调试和测试
- 支持调试模式和生产模式
- 串口通信数据必须记录十六进制格式
- 命令执行过程需要详细日志跟踪
- 支持 Web API 接口用于外部监控

## 部署约定
- 目标平台：ARM 架构（如 RK3508）
- 交叉编译支持
- 配置文件与可执行文件同目录
- 支持信号量优雅关闭

## 安全考虑
- Modbus TCP 连接数限制
- 串口访问权限检查
- Web 界面可选择性启用
- 配置文件参数验证

## 注释规范
- 结构体和主要函数必须有中文注释
- 协议相关常量需要详细说明
- 复杂算法需要步骤说明
- API 接口需要参数和返回值说明

在开发过程中，请始终考虑工业环境的可靠性要求，确保代码的健壮性和可维护性。 