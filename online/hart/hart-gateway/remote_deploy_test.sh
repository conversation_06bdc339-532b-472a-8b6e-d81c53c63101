#!/bin/bash

# HART网关远程部署和测试脚本
# 基于quick_deploy.sh，增加了自动测试功能

# ===== 配置区域 - 请根据实际情况修改 =====
TARGET_HOST="***********"           # 目标机器IP
TARGET_USER="root"                      # 目标机器用户名  
TARGET_DIR="/root/hart-gateway-2"         # 目标部署目录
MODBUS_PORT="1502"                      # Modbus TCP端口
WEB_PORT="8080"                         # Web管理端口
# ==========================================

set -e

echo "🚀 HART网关远程部署和测试"
echo "构建环境: $(uname -m)"
echo "目标环境: ARM v6 (Linux)"
echo "目标主机: $TARGET_USER@$TARGET_HOST:$TARGET_DIR"
echo ""

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法:"
    echo "  $0 deploy        # 部署网关到远程服务器"
    echo "  $0 start         # 启动远程网关服务"
    echo "  $0 stop          # 停止远程网关服务"
    echo "  $0 test          # 测试远程网关Modbus接口"
    echo "  $0 logs          # 查看远程网关日志"
    echo "  $0 all           # 完整流程：部署+启动+测试"
    echo ""
    echo "示例:"
    echo "  $0 all           # 一键部署并测试"
    echo "  $0 test          # 单独测试现有服务"
    exit 1
fi

# 检查SSH连接
check_ssh() {
    echo "🔗 检查SSH连接..."
    if ssh -o ConnectTimeout=5 "$TARGET_USER@$TARGET_HOST" "echo '连接成功'" 2>/dev/null; then
        echo "✅ SSH连接正常"
    else
        echo "❌ SSH连接失败，请检查:"
        echo "   - IP地址: $TARGET_HOST"
        echo "   - 用户名: $TARGET_USER"
        echo "   - SSH密钥或密码配置"
        exit 1
    fi
}

# 交叉编译
build_gateway() {
    echo ""
    echo "📦 交叉编译HART网关..."
    
    # 检查源文件
    if [ ! -f "simple_hart_gateway.go" ]; then
        echo "❌ 未找到 simple_hart_gateway.go"
        exit 1
    fi
    
    if [ ! -f "simple_config.json" ]; then
        echo "❌ 未找到 simple_config.json"
        exit 1
    fi
    
    # 交叉编译到ARM v6
    GOOS=linux GOARCH=arm GOARM=6 CGO_ENABLED=0 go build -ldflags="-s -w" -o simple_hart_gateway_arm simple_hart_gateway.go
    
    if [ $? -eq 0 ]; then
        echo "✅ 编译成功"
        ls -lh simple_hart_gateway_arm
    else
        echo "❌ 编译失败"
        exit 1
    fi
}

# 部署文件
deploy_files() {
    echo ""
    echo "📤 部署文件到远程服务器..."
    
    # 停止现有服务 (兼容buildroot)
    ssh "$TARGET_USER@$TARGET_HOST" "ps | grep simple_hart_gateway | grep -v grep | awk '{print \$1}' | xargs kill -9 2>/dev/null || true"
    
    # 创建目录
    ssh "$TARGET_USER@$TARGET_HOST" "mkdir -p $TARGET_DIR"
    
    # 复制文件
    scp simple_hart_gateway_arm "$TARGET_USER@$TARGET_HOST:$TARGET_DIR/simple_hart_gateway"
    scp simple_config.json "$TARGET_USER@$TARGET_HOST:$TARGET_DIR/"
    
    # 设置权限
    ssh "$TARGET_USER@$TARGET_HOST" "chmod +x $TARGET_DIR/simple_hart_gateway"
    
    echo "✅ 文件部署完成"
}

# 启动服务
start_service() {
    echo ""
    echo "🚀 启动远程HART网关服务..."
    
    # 停止现有服务 (兼容buildroot)
    ssh "$TARGET_USER@$TARGET_HOST" "ps | grep simple_hart_gateway | grep -v grep | awk '{print \$1}' | xargs kill -9 2>/dev/null || true"
    sleep 1
    
    # 启动新服务 (改进PID检测)
    ssh "$TARGET_USER@$TARGET_HOST" "
        cd $TARGET_DIR
        nohup ./simple_hart_gateway > gateway.log 2>&1 &
        sleep 1
        # 查找实际的进程PID并写入文件
        ps | grep './simple_hart_gateway' | grep -v grep | awk '{print \$1}' > gateway.pid
        echo '服务已启动，PID: '\$(cat gateway.pid)
    "
    
    sleep 3
    
    # 检查服务状态 (改进检测逻辑)
    if ssh "$TARGET_USER@$TARGET_HOST" "test -f $TARGET_DIR/gateway.pid && test -s $TARGET_DIR/gateway.pid && ps | grep './simple_hart_gateway' | grep -v grep > /dev/null"; then
        echo "✅ 网关服务启动成功"
        
        echo ""
        echo "📊 服务信息:"
        echo "   Modbus TCP: $TARGET_HOST:$MODBUS_PORT"
        echo "   Web管理界面: http://$TARGET_HOST:$WEB_PORT"
        echo "   日志文件: $TARGET_DIR/gateway.log"
        echo "   PID文件: $TARGET_DIR/gateway.pid"
        
    else
        echo "❌ 服务启动失败，查看日志:"
        ssh "$TARGET_USER@$TARGET_HOST" "cat $TARGET_DIR/gateway.log 2>/dev/null || echo '日志文件不存在'"
        exit 1
    fi
}

# 停止服务
stop_service() {
    echo ""
    echo "🛑 停止远程HART网关服务..."
    
    if ssh "$TARGET_USER@$TARGET_HOST" "test -f $TARGET_DIR/gateway.pid"; then
        ssh "$TARGET_USER@$TARGET_HOST" "kill \$(cat $TARGET_DIR/gateway.pid) 2>/dev/null || true"
        ssh "$TARGET_USER@$TARGET_HOST" "rm -f $TARGET_DIR/gateway.pid"
        echo "✅ 服务已停止"
    else
        echo "⚠️  未找到PID文件，尝试强制停止..."
        ssh "$TARGET_USER@$TARGET_HOST" "ps | grep simple_hart_gateway | grep -v grep | awk '{print \$1}' | xargs kill -9 2>/dev/null || true"
    fi
}

# 查看日志
show_logs() {
    echo ""
    echo "📋 远程网关日志 (最后20行):"
    echo "=================================="
    ssh "$TARGET_USER@$TARGET_HOST" "tail -20 $TARGET_DIR/gateway.log 2>/dev/null || echo '日志文件不存在'"
}

# 测试Modbus接口
test_modbus() {
    echo ""
    echo "🧪 测试远程HART网关Modbus接口..."
    
    # 检查测试客户端
    if [ ! -f "test/main.go" ]; then
        echo "❌ 未找到测试客户端 test/main.go"
        exit 1
    fi
    
    # 运行测试
    cd test
    echo "连接目标: $TARGET_HOST:$MODBUS_PORT"
    go run main.go test $TARGET_HOST $MODBUS_PORT
    cd ..
}

# 连续测试
test_continuous() {
    echo ""
    echo "🔄 连续测试远程HART网关..."
    
    cd test
    go run main.go continuous $TARGET_HOST $MODBUS_PORT 2s 5
    cd ..
}

# 主逻辑
case "$1" in
    "deploy")
        check_ssh
        build_gateway
        deploy_files
        ;;
        
    "start") 
        check_ssh
        start_service
        ;;
        
    "stop")
        check_ssh  
        stop_service
        ;;
        
    "test")
        test_modbus
        ;;
        
    "continuous")
        test_continuous
        ;;
        
    "logs")
        check_ssh
        show_logs
        ;;
        
    "all")
        check_ssh
        build_gateway
        deploy_files
        start_service
        echo ""
        echo "⏱️  等待服务完全启动..."
        sleep 3
        # test_modbus
        # show_logs
        ;;
        
    *)
        echo "❌ 未知参数: $1"
        echo "支持的参数: deploy, start, stop, test, continuous, logs, all"
        exit 1
        ;;
esac

echo ""
echo "✅ 操作完成"
echo ""
echo "🔧 常用命令 (兼容buildroot):"
echo "   查看实时日志: ssh $TARGET_USER@$TARGET_HOST 'tail -f $TARGET_DIR/gateway.log'"
echo "   检查进程状态: ssh $TARGET_USER@$TARGET_HOST 'ps | grep simple_hart_gateway'"
echo "   手动停止服务: ssh $TARGET_USER@$TARGET_HOST 'ps | grep simple_hart_gateway | grep -v grep | awk \"{print \\\$1}\" | xargs kill -9'"
echo "   重启服务: $0 stop && $0 start"
echo "   Web界面: http://$TARGET_HOST:$WEB_PORT" 