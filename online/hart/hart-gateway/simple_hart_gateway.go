package main

import (
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/tarm/serial"
)

// 简化配置结构体
type SimpleConfig struct {
	System SimpleSystem `json:"system"`
	Serial SimpleSerial `json:"serial"`
	Modbus SimpleModbus `json:"modbus"`
	Web    SimpleWeb    `json:"web"`
	MQTT   SimpleMQTT   `json:"mqtt,omitempty"` // 新增MQTT配置
	Node   SimpleNode   `json:"node"`
	Memory SimpleMemory `json:"memory"`
}

// 新增MQTT配置结构体
type SimpleMQTT struct {
	Enabled       bool   `json:"enabled"`
	Broker        string `json:"broker"`
	Username      string `json:"username,omitempty"`
	Password      string `json:"password,omitempty"`
	DeviceID      string `json:"device_id"`
	RequestTopic  string `json:"request_topic,omitempty"`  // 默认: device/{device_id}/request
	ResponseTopic string `json:"response_topic,omitempty"` // 默认: device/{device_id}/response
	QOS           byte   `json:"qos,omitempty"`            // 默认: 1
}

type SimpleSystem struct {
	PollInterval       int `json:"poll_interval"`        // 轮询间隔(ms)
	CommandInterval    int `json:"command_interval"`     // 命令间隔(ms)
	WebRefreshInterval int `json:"web_refresh_interval"` // Web刷新间隔(ms)
}

type SimpleSerial struct {
	Port     string `json:"port"`
	Baudrate int    `json:"baudrate"`
	Databits int    `json:"databits"`
	Stopbits int    `json:"stopbits"`
	Parity   string `json:"parity"`
	Timeout  int    `json:"timeout"`
}

type SimpleModbus struct {
	Enabled bool `json:"enabled"`
	Port    int  `json:"port"`
}

type SimpleWeb struct {
	Enabled bool `json:"enabled"`
	Port    int  `json:"port"`
}

type SimpleNode struct {
	Address  int             `json:"address"`
	Enabled  bool            `json:"enabled"`
	Commands []SimpleCommand `json:"commands"`
}

type SimpleCommand struct {
	Command         int     `json:"command"`
	Description     string  `json:"description"`
	Enabled         bool    `json:"enabled"`
	ModbusRegister  int     `json:"modbus_register"`
	DataType        string  `json:"data_type"`
	Unit            string  `json:"unit,omitempty"`
	AlarmLow        float64 `json:"alarm_low,omitempty"`
	AlarmHigh       float64 `json:"alarm_high,omitempty"`
	CommandType     string  `json:"command_type,omitempty"`      // "standard" 或 "custom"
	CustomInputData *string `json:"custom_input_data,omitempty"` // 自定义输入数据（十六进制字符串）
}

type SimpleMemory struct {
	TotalSize           int `json:"total_size"`
	InputRegistersStart int `json:"input_registers_start"`
}

// HART 协议结构体
type SimpleHARTFrame struct {
	Preamble  []byte
	Delimiter byte
	Address   byte
	Command   byte
	ByteCount byte
	Data      []byte
	Checksum  byte
}

type SimpleHARTResponse struct {
	Address      byte
	Command      byte
	ResponseCode byte
	DeviceStatus byte
	Data         []byte
	IsValid      bool
}

// Modbus 结构体
type SimpleModbusRequest struct {
	TransactionID uint16
	ProtocolID    uint16
	Length        uint16
	UnitID        uint8
	FunctionCode  uint8
	Data          []byte
}

type SimpleModbusResponse struct {
	TransactionID uint16
	ProtocolID    uint16
	Length        uint16
	UnitID        uint8
	FunctionCode  uint8
	Data          []byte
	Exception     uint8
}

// 简化网关结构体
type SimpleGateway struct {
	config      *SimpleConfig
	logger      *log.Logger
	serialPort  *serial.Port
	memoryData  []byte
	isRunning   bool
	stopChan    chan bool
	mutex       sync.RWMutex
	serialMutex sync.Mutex // 串口访问互斥锁，确保一写一读原子性
	lastValues  map[int]interface{}
	mqttClient  mqtt.Client // MQTT客户端
}

// 协议常量
const (
	SIMPLE_HART_PREAMBLE_LENGTH = 5
	SIMPLE_HART_DELIMITER_SHORT = 0x02 // 主设备发送
	SIMPLE_HART_RESPONSE_SHORT  = 0x06 // 从设备响应(短帧)
	SIMPLE_HART_RESPONSE_LONG   = 0x86 // 从设备响应(长帧)

	SIMPLE_MB_FC_READ_INPUT_REGISTERS = 0x04
	SIMPLE_MB_EX_ILLEGAL_FUNCTION     = 0x01
	SIMPLE_MB_EX_ILLEGAL_DATA_ADDRESS = 0x02
)

func main() {
	configFile := "simple_config.json"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	config, err := loadSimpleConfig(configFile)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	gateway, err := NewSimpleGateway(config)
	if err != nil {
		log.Fatalf("创建网关失败: %v", err)
	}

	if err := gateway.Start(); err != nil {
		log.Fatalf("启动网关失败: %v", err)
	}

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	gateway.logger.Println("=== 简化版 HART-Modbus 网关已启动 ===")
	gateway.logger.Printf("节点地址: %d, 命令数: %d",
		gateway.config.Node.Address, len(gateway.config.Node.Commands))
	<-sigChan

	gateway.logger.Println("收到停止信号，正在关闭网关...")
	gateway.Stop()
}

func NewSimpleGateway(config *SimpleConfig) (*SimpleGateway, error) {
	gateway := &SimpleGateway{
		config:     config,
		memoryData: make([]byte, config.Memory.TotalSize),
		stopChan:   make(chan bool),
		lastValues: make(map[int]interface{}),
	}

	gateway.logger = log.New(os.Stdout, "[简化网关] ", log.LstdFlags)
	return gateway, nil
}

func (g *SimpleGateway) Start() error {
	if g.isRunning {
		return fmt.Errorf("网关已在运行")
	}

	// 尝试初始化串口，但不因失败而停止整个网关
	if err := g.initSerial(); err != nil {
		g.logger.Printf("⚠️ 串口初始化失败，将以仅Modbus模式运行: %v", err)
		g.serialPort = nil
	}

	// 初始化MQTT客户端
	if g.config.MQTT.Enabled {
		if err := g.initMQTT(); err != nil {
			g.logger.Printf("⚠️ MQTT初始化失败: %v", err)
		}
	}

	if g.config.Modbus.Enabled {
		go g.startModbusServer()
	}

	if g.config.Web.Enabled {
		go g.startWebServer()
	}

	// 只有当串口可用时才启动轮询
	if g.serialPort != nil {
		go g.startPolling()
	} else {
		g.logger.Println("⚠️ 跳过HART轮询，网关将只提供Modbus接口")
	}

	g.isRunning = true
	return nil
}

func (g *SimpleGateway) Stop() error {
	if !g.isRunning {
		return fmt.Errorf("网关未运行")
	}

	close(g.stopChan)

	if g.serialPort != nil {
		g.serialPort.Close()
	}

	// 关闭MQTT连接
	if g.mqttClient != nil && g.mqttClient.IsConnected() {
		g.mqttClient.Disconnect(250)
		g.logger.Println("[MQTT] 连接已关闭")
	}

	g.isRunning = false
	g.logger.Println("网关已停止")
	return nil
}

func (g *SimpleGateway) initSerial() error {
	config := &serial.Config{
		Name:        g.config.Serial.Port,
		Baud:        g.config.Serial.Baudrate,
		Size:        byte(g.config.Serial.Databits),
		StopBits:    serial.StopBits(g.config.Serial.Stopbits),
		ReadTimeout: time.Duration(g.config.Serial.Timeout) * time.Millisecond,
	}

	switch g.config.Serial.Parity {
	case "odd":
		config.Parity = serial.ParityOdd
	case "even":
		config.Parity = serial.ParityEven
	default:
		config.Parity = serial.ParityNone
	}

	port, err := serial.OpenPort(config)
	if err != nil {
		return err
	}

	g.serialPort = port
	g.logger.Printf("串口已初始化: %s @ %d bps", g.config.Serial.Port, g.config.Serial.Baudrate)
	return nil
}

func (g *SimpleGateway) initMQTT() error {
	// 设置默认主题
	requestTopic := g.config.MQTT.RequestTopic
	if requestTopic == "" {
		requestTopic = fmt.Sprintf("device/%s/request", g.config.MQTT.DeviceID)
	}

	responseTopic := g.config.MQTT.ResponseTopic
	if responseTopic == "" {
		responseTopic = fmt.Sprintf("device/%s/response", g.config.MQTT.DeviceID)
	}

	// 设置默认QOS
	qos := g.config.MQTT.QOS
	if qos == 0 {
		qos = 1
	}

	// 创建MQTT客户端选项
	opts := mqtt.NewClientOptions().
		AddBroker(g.config.MQTT.Broker).
		SetClientID(fmt.Sprintf("hart_gateway_%s", g.config.MQTT.DeviceID)).
		SetCleanSession(true).
		SetAutoReconnect(true).
		SetKeepAlive(60 * time.Second).
		SetOnConnectHandler(func(client mqtt.Client) {
			g.logger.Printf("[MQTT] 连接成功: %s", g.config.MQTT.Broker)
			// 订阅请求主题
			if token := client.Subscribe(requestTopic, qos, g.handleMQTTRequest); token.Wait() && token.Error() != nil {
				g.logger.Printf("[MQTT] 订阅失败: %v", token.Error())
			} else {
				g.logger.Printf("[MQTT] 已订阅请求主题: %s", requestTopic)
			}
		}).
		SetConnectionLostHandler(func(client mqtt.Client, err error) {
			g.logger.Printf("[MQTT] 连接丢失: %v", err)
		})

	// 设置用户名和密码
	if g.config.MQTT.Username != "" {
		opts.SetUsername(g.config.MQTT.Username)
	}
	if g.config.MQTT.Password != "" {
		opts.SetPassword(g.config.MQTT.Password)
	}

	// 创建客户端并连接
	g.mqttClient = mqtt.NewClient(opts)
	if token := g.mqttClient.Connect(); token.Wait() && token.Error() != nil {
		return fmt.Errorf("MQTT连接失败: %v", token.Error())
	}

	g.logger.Printf("[MQTT] 客户端初始化成功，设备ID: %s", g.config.MQTT.DeviceID)
	return nil
}

// handleMQTTRequest 处理通过MQTT接收到的Modbus请求
func (g *SimpleGateway) handleMQTTRequest(client mqtt.Client, msg mqtt.Message) {
	g.logger.Printf("[MQTT] 收到Modbus请求，数据长度: %d", len(msg.Payload()))

	// 解析Modbus TCP请求
	request, err := g.parseModbusRequest(msg.Payload())
	if err != nil {
		g.logger.Printf("[MQTT] 解析Modbus请求失败: %v", err)
		return
	}

	// 处理Modbus请求
	response := g.processModbusRequest(request)
	responseData := g.buildModbusResponse(response)

	// 发布响应到MQTT
	responseTopic := g.config.MQTT.ResponseTopic
	if responseTopic == "" {
		responseTopic = fmt.Sprintf("device/%s/response", g.config.MQTT.DeviceID)
	}

	qos := g.config.MQTT.QOS
	if qos == 0 {
		qos = 1
	}

	if token := client.Publish(responseTopic, qos, false, responseData); token.Wait() && token.Error() != nil {
		g.logger.Printf("[MQTT] 发布响应失败: %v", token.Error())
	} else {
		g.logger.Printf("[MQTT] 响应已发布，数据长度: %d", len(responseData))
	}
}

func (g *SimpleGateway) startPolling() {
	if !g.config.Node.Enabled {
		g.logger.Println("节点已禁用，跳过轮询")
		return
	}

	g.logger.Printf("开始轮询节点 %d", g.config.Node.Address)

	// 启动单个轮询协程，串行执行所有命令
	go g.serialCommandPolling()
}

// serialCommandPolling 串行执行命令轮询，确保串口访问的原子性
func (g *SimpleGateway) serialCommandPolling() {
	// 收集启用的命令
	var enabledCommands []SimpleCommand
	for _, cmd := range g.config.Node.Commands {
		if cmd.Enabled {
			enabledCommands = append(enabledCommands, cmd)
			g.logger.Printf("命令 %d (%s) 已启用",
				cmd.Command, cmd.Description)
		}
	}

	if len(enabledCommands) == 0 {
		g.logger.Println("无启用的命令，跳过轮询")
		return
	}

	g.logger.Printf("开始串行轮询，共 %d 个命令", len(enabledCommands))

	// 使用配置文件中的轮询间隔
	pollInterval := time.Duration(g.config.System.PollInterval) * time.Millisecond
	ticker := time.NewTicker(pollInterval)
	g.logger.Printf("使用轮询间隔: %dms", g.config.System.PollInterval)
	defer ticker.Stop()

	for {
		select {
		case <-g.stopChan:
			g.logger.Println("停止命令轮询")
			return
		case <-ticker.C:
			// 依次执行所有启用的命令
			for _, cmd := range enabledCommands {
				g.executeCommand(&cmd)
				// 串口操作需要间隔，防止设备响应不过来
				time.Sleep(time.Duration(g.config.System.CommandInterval) * time.Millisecond)
			}
		}
	}
}

func (g *SimpleGateway) executeCommand(cmd *SimpleCommand) {
	// 串口访问必须加锁，确保一写一读的原子性
	g.serialMutex.Lock()
	defer g.serialMutex.Unlock()

	g.logger.Printf("[CMD%d] 执行: %s", cmd.Command, cmd.Description)

	// 检查串口是否可用
	if g.serialPort == nil {
		g.logger.Printf("[CMD%d] 串口不可用，跳过执行", cmd.Command)
		return
	}

	var frameData []byte

	if cmd.CommandType == "custom" && cmd.CustomInputData != nil {
		// 对于自定义命令，custom_input_data是完整的HART数据帧，直接发送
		customData, err := g.parseCustomInputData(*cmd.CustomInputData)
		if err != nil {
			g.logger.Printf("[CMD%d] 解析自定义输入数据失败: %v", cmd.Command, err)
			return
		}
		frameData = customData
		g.logger.Printf("[CMD%d] 使用完整自定义帧，长度: %d", cmd.Command, len(frameData))
	} else {
		// 对于标准命令，构建标准HART帧
		frame := g.buildHARTFrame(byte(g.config.Node.Address), byte(cmd.Command), nil)
		frameData = frame.ToBytes()
		g.logger.Printf("[CMD%d] 使用标准HART帧", cmd.Command)
	}

	g.logger.Printf("[CMD%d] 发送 → % X", cmd.Command, frameData)

	// 发送命令
	n, err := g.serialPort.Write(frameData)
	if err != nil {
		g.logger.Printf("[CMD%d] 发送失败: %v", cmd.Command, err)
		return
	}

	if n != len(frameData) {
		g.logger.Printf("[CMD%d] 发送不完整: %d/%d", cmd.Command, n, len(frameData))
		return
	}

	// 读取响应
	buffer := make([]byte, 256)
	n, err = g.serialPort.Read(buffer)
	if err != nil {
		g.logger.Printf("[CMD%d] 接收失败: %v", cmd.Command, err)
		return
	}

	g.logger.Printf("[CMD%d] 接收 ← % X", cmd.Command, buffer[:n])

	if cmd.CommandType == "custom" {
		// 对于自定义命令，不解析HART响应，直接处理原始数据
		g.logger.Printf("[CMD%d] 自定义命令，直接存储完整响应帧，长度: %d", cmd.Command, n)
		rawResponse := &SimpleHARTResponse{
			Command: byte(cmd.Command),
			Data:    buffer[:n], // 存储完整的原始响应数据
			IsValid: true,
		}
		g.processResponse(rawResponse, cmd)
	} else {
		// 对于标准命令，解析HART响应
		response, err := g.parseHARTResponse(buffer[:n])
		if err != nil {
			g.logger.Printf("[CMD%d] 解析失败: %v", cmd.Command, err)
			return
		}
		g.processResponse(response, cmd)
	}
}

// parseCustomInputData 解析自定义输入数据字符串为字节数组
func (g *SimpleGateway) parseCustomInputData(input string) ([]byte, error) {
	if input == "" {
		return nil, nil
	}

	// 转为大写
	input = strings.ToUpper(input)

	// 支持三种格式：
	// 1. "0x01,0x02,0x03" (逗号分隔的十六进制)
	// 2. "FF FF FF FF FF 02 80 00 00 82" (空格分隔的十六进制)
	// 3. "010203" (连续十六进制字符串)

	var data []byte
	var err error

	if strings.Contains(input, ",") {
		// 处理逗号分隔格式
		parts := strings.Split(input, ",")
		data = make([]byte, len(parts))

		for i, part := range parts {
			// 移除0x前缀和空格
			part = strings.TrimPrefix(strings.TrimSpace(part), "0X")

			if len(part) == 0 {
				return nil, fmt.Errorf("空的十六进制值在位置 %d", i)
			}

			val, err := strconv.ParseUint(part, 16, 8)
			if err != nil {
				return nil, fmt.Errorf("解析十六进制值 '%s' 失败: %v", part, err)
			}
			data[i] = byte(val)
		}
	} else if strings.Contains(input, " ") {
		// 处理空格分隔格式
		parts := strings.Fields(input) // 自动处理多个空格
		data = make([]byte, len(parts))

		for i, part := range parts {
			// 移除0x前缀
			part = strings.TrimPrefix(part, "0X")

			if len(part) == 0 {
				return nil, fmt.Errorf("空的十六进制值在位置 %d", i)
			}

			if len(part) > 2 {
				return nil, fmt.Errorf("十六进制值 '%s' 长度超过2位", part)
			}

			val, err := strconv.ParseUint(part, 16, 8)
			if err != nil {
				return nil, fmt.Errorf("解析十六进制值 '%s' 失败: %v", part, err)
			}
			data[i] = byte(val)
		}
	} else {
		// 处理连续十六进制字符串格式
		// 移除所有空格
		cleanInput := strings.ReplaceAll(input, " ", "")

		if len(cleanInput)%2 != 0 {
			return nil, fmt.Errorf("十六进制字符串长度必须为偶数: %s", cleanInput)
		}

		data, err = hex.DecodeString(cleanInput)
		if err != nil {
			return nil, fmt.Errorf("解析十六进制字符串失败: %v", err)
		}
	}

	g.logger.Printf("[自定义数据解析] 输入: %s → 输出: % X", input, data)
	return data, nil
}

func (g *SimpleGateway) buildHARTFrame(address, command byte, data []byte) *SimpleHARTFrame {
	// 在 HART 协议中，点对点模式使用地址 0x80
	// 0x80 = 0x00 (设备地址0) | 0x80 (点对点模式标志)
	hartAddress := byte(0x80) // 点对点模式，目标设备地址为0

	frame := &SimpleHARTFrame{
		Preamble:  make([]byte, SIMPLE_HART_PREAMBLE_LENGTH),
		Delimiter: SIMPLE_HART_DELIMITER_SHORT,
		Address:   hartAddress,
		Command:   command,
		ByteCount: byte(len(data)),
		Data:      data,
	}

	for i := 0; i < SIMPLE_HART_PREAMBLE_LENGTH; i++ {
		frame.Preamble[i] = 0xFF
	}

	frame.Checksum = g.calculateChecksum(frame)

	g.logger.Printf("[HART帧构建] 使用点对点模式地址: 0x%02X (设备地址:%d)",
		hartAddress, address)

	return frame
}

func (g *SimpleGateway) calculateChecksum(frame *SimpleHARTFrame) byte {
	checksum := frame.Delimiter ^ frame.Address ^ frame.Command ^ frame.ByteCount
	for _, b := range frame.Data {
		checksum ^= b
	}

	g.logger.Printf("[校验] %02X^%02X^%02X^%02X = %02X",
		frame.Delimiter, frame.Address, frame.Command, frame.ByteCount, checksum)

	return checksum
}

func (f *SimpleHARTFrame) ToBytes() []byte {
	var buf bytes.Buffer
	buf.Write(f.Preamble)
	buf.WriteByte(f.Delimiter)
	buf.WriteByte(f.Address)
	buf.WriteByte(f.Command)
	buf.WriteByte(f.ByteCount)
	if len(f.Data) > 0 {
		buf.Write(f.Data)
	}
	buf.WriteByte(f.Checksum)
	return buf.Bytes()
}

func (g *SimpleGateway) parseHARTResponse(data []byte) (*SimpleHARTResponse, error) {
	if len(data) < 7 {
		return nil, fmt.Errorf("数据长度不足: %d", len(data))
	}

	g.logger.Printf("[HART解析] 原始数据: % X", data)

	// 查找响应定界符 0x06 或 0x86
	delimiterIndex := -1
	delimiter := byte(0x00)
	for i := 0; i < len(data)-6; i++ {
		if data[i] == SIMPLE_HART_RESPONSE_SHORT || data[i] == SIMPLE_HART_RESPONSE_LONG {
			delimiterIndex = i
			delimiter = data[i]
			g.logger.Printf("[HART解析] 找到响应定界符0x%02X在位置 %d", delimiter, i)
			break
		}
	}

	if delimiterIndex == -1 {
		return nil, fmt.Errorf("未找到响应定界符(0x06或0x86)")
	}

	offset := delimiterIndex + 1
	response := &SimpleHARTResponse{
		Address: data[offset],
		Command: data[offset+1],
	}

	byteCount := data[offset+2]
	offset += 3

	g.logger.Printf("[HART解析] 地址:0x%02X, 命令:%d, 字节数:%d",
		response.Address, response.Command, byteCount)

	if byteCount >= 2 {
		response.ResponseCode = data[offset]
		response.DeviceStatus = data[offset+1]
		offset += 2

		g.logger.Printf("[HART解析] 响应码:%d, 设备状态:0x%02X",
			response.ResponseCode, response.DeviceStatus)

		if byteCount > 2 {
			dataLen := int(byteCount) - 2
			if offset+dataLen <= len(data) {
				response.Data = data[offset : offset+dataLen]
				g.logger.Printf("[HART解析] 数据部分: % X (长度:%d)",
					response.Data, len(response.Data))
			}
		}
	}

	response.IsValid = true
	return response, nil
}

func (g *SimpleGateway) processResponse(response *SimpleHARTResponse, cmd *SimpleCommand) {
	if !response.IsValid {
		g.logger.Printf("[CMD%d] 响应无效", response.Command)
		return
	}

	if response.ResponseCode != 0 {
		g.logger.Printf("[CMD%d] 响应错误码: %d", response.Command, response.ResponseCode)
		return
	}

	g.logger.Printf("[CMD%d] 响应有效，数据长度: %d", response.Command, len(response.Data))

	// 根据数据类型处理
	var dataToStore []byte

	switch cmd.DataType {
	case "float32":
		if len(response.Data) >= 5 {
			// HART 主变量格式：单位码(1字节) + IEEE754浮点数(4字节)
			unit := response.Data[0]
			value := math.Float32frombits(binary.BigEndian.Uint32(response.Data[1:5]))

			g.logger.Printf("[CMD%d] 浮点值: %.2f %s (单位码:%d)",
				response.Command, value, cmd.Unit, unit)

			// 检查告警
			if cmd.AlarmLow != 0 || cmd.AlarmHigh != 0 {
				if float64(value) < cmd.AlarmLow {
					g.logger.Printf("[CMD%d] ⚠️ 告警: 值过低 %.2f < %.2f",
						response.Command, value, cmd.AlarmLow)
				} else if float64(value) > cmd.AlarmHigh {
					g.logger.Printf("[CMD%d] ⚠️ 告警: 值过高 %.2f > %.2f",
						response.Command, value, cmd.AlarmHigh)
				}
			}

			g.lastValues[cmd.Command] = value
			dataToStore = response.Data
		}
	case "raw_frame":
		// 对于自定义命令，存储完整的响应帧数据
		if cmd.CommandType == "custom" {
			g.logger.Printf("[CMD%d] 自定义命令原始帧存储，完整响应长度: %d", response.Command, len(response.Data))
			g.logger.Printf("[CMD%d] 完整响应帧: % X", response.Command, response.Data)
		} else {
			g.logger.Printf("[CMD%d] 标准命令原始帧存储，数据部分长度: %d", response.Command, len(response.Data))
			g.logger.Printf("[CMD%d] 数据部分: % X", response.Command, response.Data)
		}

		// 存储完整的响应数据，不进行任何解析
		g.lastValues[cmd.Command] = response.Data
		dataToStore = response.Data

		// 存储原始响应数据
		g.logger.Printf("[CMD%d] 存储原始响应数据，长度: %d", response.Command, len(response.Data))
	default:
		g.logger.Printf("[CMD%d] 原始数据: % X", response.Command, response.Data)
		g.lastValues[cmd.Command] = response.Data
		dataToStore = response.Data
	}

	// 更新 Modbus 寄存器
	if len(dataToStore) > 0 {
		g.updateModbusRegister(cmd.ModbusRegister, dataToStore)
	}
}

func (g *SimpleGateway) updateModbusRegister(register int, data []byte) {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	offset := (register - g.config.Memory.InputRegistersStart) * 2
	if offset >= 0 && offset+len(data) <= len(g.memoryData) {
		copy(g.memoryData[offset:offset+len(data)], data)
		g.logger.Printf("[Modbus] 更新寄存器 %d, 偏移 %d, 长度 %d",
			register, offset, len(data))
	}
}

func (g *SimpleGateway) startModbusServer() {
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", g.config.Modbus.Port))
	if err != nil {
		g.logger.Printf("Modbus 服务器启动失败: %v", err)
		return
	}
	defer listener.Close()

	g.logger.Printf("Modbus TCP 服务器已启动，端口: %d", g.config.Modbus.Port)

	for {
		conn, err := listener.Accept()
		if err != nil {
			continue
		}
		go g.handleModbusConnection(conn)
	}
}

func (g *SimpleGateway) handleModbusConnection(conn net.Conn) {
	defer conn.Close()
	g.logger.Printf("新 Modbus 客户端: %s", conn.RemoteAddr())

	buffer := make([]byte, 1024)
	for {
		n, err := conn.Read(buffer)
		if err != nil {
			break
		}

		request, err := g.parseModbusRequest(buffer[:n])
		if err != nil {
			continue
		}

		response := g.processModbusRequest(request)
		responseData := g.buildModbusResponse(response)

		conn.Write(responseData)
	}
}

func (g *SimpleGateway) parseModbusRequest(data []byte) (*SimpleModbusRequest, error) {
	if len(data) < 8 {
		return nil, fmt.Errorf("数据长度不足")
	}

	return &SimpleModbusRequest{
		TransactionID: binary.BigEndian.Uint16(data[0:2]),
		ProtocolID:    binary.BigEndian.Uint16(data[2:4]),
		Length:        binary.BigEndian.Uint16(data[4:6]),
		UnitID:        data[6],
		FunctionCode:  data[7],
		Data:          data[8:],
	}, nil
}

func (g *SimpleGateway) processModbusRequest(request *SimpleModbusRequest) *SimpleModbusResponse {
	response := &SimpleModbusResponse{
		TransactionID: request.TransactionID,
		ProtocolID:    request.ProtocolID,
		UnitID:        request.UnitID,
		FunctionCode:  request.FunctionCode,
	}

	switch request.FunctionCode {
	case SIMPLE_MB_FC_READ_INPUT_REGISTERS:
		g.handleReadInputRegisters(request, response)
	default:
		response.Exception = SIMPLE_MB_EX_ILLEGAL_FUNCTION
	}

	return response
}

func (g *SimpleGateway) handleReadInputRegisters(request *SimpleModbusRequest, response *SimpleModbusResponse) {
	if len(request.Data) < 4 {
		response.Exception = SIMPLE_MB_EX_ILLEGAL_DATA_ADDRESS
		return
	}

	startAddr := binary.BigEndian.Uint16(request.Data[0:2])
	quantity := binary.BigEndian.Uint16(request.Data[2:4])

	offset := int(startAddr-uint16(g.config.Memory.InputRegistersStart)) * 2
	dataLen := int(quantity) * 2

	g.mutex.RLock()
	defer g.mutex.RUnlock()

	if offset < 0 || offset+dataLen > len(g.memoryData) {
		response.Exception = SIMPLE_MB_EX_ILLEGAL_DATA_ADDRESS
		return
	}

	response.Data = make([]byte, 1+dataLen)
	response.Data[0] = byte(dataLen)
	copy(response.Data[1:], g.memoryData[offset:offset+dataLen])
	response.Length = uint16(3 + len(response.Data))
}

func (g *SimpleGateway) buildModbusResponse(response *SimpleModbusResponse) []byte {
	var buf bytes.Buffer

	binary.Write(&buf, binary.BigEndian, response.TransactionID)
	binary.Write(&buf, binary.BigEndian, response.ProtocolID)
	binary.Write(&buf, binary.BigEndian, response.Length)
	buf.WriteByte(response.UnitID)

	if response.Exception != 0 {
		buf.WriteByte(response.FunctionCode | 0x80)
		buf.WriteByte(response.Exception)
	} else {
		buf.WriteByte(response.FunctionCode)
		if len(response.Data) > 0 {
			buf.Write(response.Data)
		}
	}

	return buf.Bytes()
}

func (g *SimpleGateway) startWebServer() {
	mux := http.NewServeMux()

	mux.HandleFunc("/", g.handleIndex)
	mux.HandleFunc("/api/status", g.handleAPIStatus)
	mux.HandleFunc("/api/values", g.handleAPIValues)

	go func() {
		addr := fmt.Sprintf(":%d", g.config.Web.Port)
		g.logger.Printf("Web 服务器已启动，端口: %d", g.config.Web.Port)
		if err := http.ListenAndServe(addr, mux); err != nil {
			g.logger.Printf("Web 服务器错误: %v", err)
		}
	}()
}

func (g *SimpleGateway) handleIndex(w http.ResponseWriter, r *http.Request) {
	html := fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <title>简化版 HART-Modbus 网关</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; }
        .card { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        table { width: 100%%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #34495e; color: white; }
        .status-active { color: #27ae60; font-weight: bold; }
        .status-inactive { color: #e74c3c; }
        .value { font-family: monospace; background: #ecf0f1; padding: 4px 8px; border-radius: 4px; }
        .refresh-btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 简化版 HART-Modbus 网关</h1>
            <p>单节点多命令配置 - 实时监控界面</p>
        </div>
        
        <div class="card">
            <h2>📡 节点信息</h2>
            <table>
                <tr><th>属性</th><th>值</th></tr>
                <tr><td>节点地址</td><td><span class="value">%d</span></td></tr>
                <tr><td>运行状态</td><td><span class="status-active">● 运行中</span></td></tr>
                <tr><td>启用命令数</td><td><span class="value">%d</span></td></tr>
                <tr><td>轮询间隔</td><td><span class="value">%dms</span></td></tr>
                <tr><td>命令间隔</td><td><span class="value">%dms</span></td></tr>
            </table>
        </div>
        
        <div class="card">
            <h2>⚙️ 命令配置</h2>
            <table>
                <tr><th>命令</th><th>描述</th><th>状态</th><th>类型</th><th>Modbus寄存器</th><th>数据类型</th><th>自定义输入</th></tr>`,
		g.config.Node.Address, g.getEnabledCommandCount(),
		g.config.System.PollInterval, g.config.System.CommandInterval)

	for _, cmd := range g.config.Node.Commands {
		status := `<span class="status-inactive">● 禁用</span>`
		if cmd.Enabled {
			status = `<span class="status-active">● 启用</span>`
		}

		cmdType := "标准"
		if cmd.CommandType == "custom" {
			cmdType = "<span style='color: #e67e22;'>自定义</span>"
		}

		customInput := "-"
		if cmd.CustomInputData != nil && *cmd.CustomInputData != "" {
			customInput = fmt.Sprintf("<span class='value'>%s</span>", *cmd.CustomInputData)
		}

		html += fmt.Sprintf(`
                <tr>
                    <td><span class="value">%d</span></td>
                    <td>%s</td>
                    <td>%s</td>
                    <td>%s</td>
                    <td><span class="value">%d</span></td>
                    <td><span class="value">%s</span></td>
                    <td>%s</td>
                </tr>`,
			cmd.Command, cmd.Description, status, cmdType, cmd.ModbusRegister, cmd.DataType, customInput)
	}

	html += `
            </table>
        </div>
        
        <div class="card">
            <h2>📊 实时数据</h2>
            <button class="refresh-btn" onclick="updateValues()">🔄 刷新数据</button>
            <div id="values" style="margin-top: 15px;">加载中...</div>
        </div>
    </div>
    
    <script>
        function updateValues() {
            fetch('/api/values')
                .then(response => response.json())
                .then(data => {
                    let html = '<table><tr><th>命令</th><th>最新值</th><th>更新时间</th></tr>';
                    for (let cmd in data) {
                        let value = data[cmd];
                        if (typeof value === 'number') {
                            value = value.toFixed(3);
                        }
                        html += '<tr><td><span class="value">' + cmd + '</span></td><td><span class="value">' + 
                               JSON.stringify(value) + '</span></td><td>' + new Date().toLocaleTimeString() + '</td></tr>';
                    }
                    html += '</table>';
                    document.getElementById('values').innerHTML = html;
                })
                .catch(err => {
                    document.getElementById('values').innerHTML = '<p style="color: red;">获取数据失败: ' + err + '</p>';
                });
        }
        
        // 自动刷新
        setInterval(updateValues, %d);
        updateValues();
    </script>
</body>
</html>`

	html = fmt.Sprintf(html, g.config.System.WebRefreshInterval)
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

func (g *SimpleGateway) getEnabledCommandCount() int {
	count := 0
	for _, cmd := range g.config.Node.Commands {
		if cmd.Enabled {
			count++
		}
	}
	return count
}

func (g *SimpleGateway) handleAPIStatus(w http.ResponseWriter, r *http.Request) {
	status := map[string]interface{}{
		"running":          g.isRunning,
		"node_address":     g.config.Node.Address,
		"total_commands":   len(g.config.Node.Commands),
		"enabled_commands": g.getEnabledCommandCount(),
		"serial_port":      g.config.Serial.Port,
		"modbus_port":      g.config.Modbus.Port,
		"web_port":         g.config.Web.Port,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

func (g *SimpleGateway) handleAPIValues(w http.ResponseWriter, r *http.Request) {
	g.mutex.RLock()
	values := make(map[string]interface{})
	for k, v := range g.lastValues {
		values[fmt.Sprintf("CMD_%d", k)] = v
	}
	g.mutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(values)
}

func loadSimpleConfig(filename string) (*SimpleConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var config SimpleConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	return &config, nil
}
